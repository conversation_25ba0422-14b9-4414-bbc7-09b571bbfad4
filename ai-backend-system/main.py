#!/usr/bin/env python3
# ai-backend-system/main.py
"""
AI 后端系统 (ai-backend-system) 的程序入口模块 (main.py)

该模块是 AI 后端系统的启动点，负责：
1. 加载系统配置 (从 config/config.yaml 文件)
2. 初始化日志系统
3. 检查必要的环境变量
4. 加载 Flask 应用 (从 services/wecom/callback.py)
5. 支持开发模式 (Flask 内置服务器) 和生产模式 (Gunicorn)
6. 确保与企业微信回调服务和智能客服逻辑无缝集成
7. 初始化并协调多 Agent 协作 (TeachingAssistantAgent, ReviewAgent, CrawlerEngineerAgent)
"""
import os
import sys
import logging
import argparse
import socket
import traceback
import asyncio

# 确保 ai-backend-system 目录在 Python 路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# 导入配置加载器
from config import get_config

# 导入 Agent 和记忆管理器
from autogen_agentchat.teams import SelectorGroupChat
from autogen_agentchat.conditions import TextMentionTermination
from autogen_ext.models.openai import OpenAIChatCompletionClient
from agents.customer_service_team.teaching_assistant_agent import TeachingAssistantAgent
from agents.customer_service_team.review_agent import ReviewAgent
from agents.dev_team.crawler_engineer_agent import CrawlerEngineerAgent
from agents.customer_service_team.teaching_memory_manager import TeachingMemoryManager

# 创建日志目录
log_dir = os.path.join(current_dir, "logs")
os.makedirs(log_dir, exist_ok=True)

# 初始化日志系统
def setup_logging():
    """
    初始化日志系统

    从配置文件加载日志设置，并配置日志系统
    """
    try:
        config = get_config()
        try:
            logging_config = config['logging']
        except KeyError:
            logging_config = {}

        try:
            log_level = logging_config['log_level'].upper()
        except (KeyError, AttributeError):
            log_level = "INFO"

        try:
            log_file_path = logging_config['log_file_path']
        except KeyError:
            log_file_path = os.path.join(log_dir, "ai_backend_system.log")

        try:
            log_format = logging_config['log_format']
        except KeyError:
            log_format = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"

        os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

        logging.basicConfig(
            level=getattr(logging, log_level),
            format=log_format,
            handlers=[
                logging.FileHandler(log_file_path),
                logging.StreamHandler()
            ]
        )

        logging.getLogger("werkzeug").setLevel(logging.WARNING)
        logging.getLogger("urllib3").setLevel(logging.WARNING)

        logger = logging.getLogger(__name__)
        logger.info(f"日志系统初始化完成，日志级别: {log_level}, 日志文件: {log_file_path}")
        return logger
    except Exception as e:
        print(f"警告: 日志配置加载失败: {e}")
        print(traceback.format_exc())

        default_log_file = os.path.join(log_dir, "ai_backend_system.log")
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
            handlers=[
                logging.FileHandler(default_log_file),
                logging.StreamHandler()
            ]
        )

        logger = logging.getLogger(__name__)
        logger.warning(f"使用默认日志配置，日志文件: {default_log_file}")
        return logger

# 检查环境变量和配置
def check_environment(logger):
    """
    检查必要的环境变量和配置
    """
    try:
        config = get_config()
        try:
            wecom_config = config['wecom']
        except KeyError:
            logger.error("缺少企业微信配置部分")
            return False

        required_configs = ['Token', 'EncodingAESKey', 'CorpID', 'Secret', 'AgentID']
        missing_configs = [config_item for config_item in required_configs if config_item not in wecom_config]

        if missing_configs:
            logger.error(f"缺少必要的企业微信配置: {', '.join(missing_configs)}")
            return False

        for config_item in required_configs:
            try:
                if not wecom_config[config_item]:
                    logger.error(f"企业微信配置 '{config_item}' 的值无效或为空")
                    return False
            except KeyError:
                logger.error(f"企业微信配置 '{config_item}' 不存在")
                return False

        logger.info("环境变量和配置检查通过")
        return True
    except Exception as e:
        logger.error(f"环境检查失败: {e}")
        logger.error(traceback.format_exc())
        return False

# 获取本机IP地址
def get_local_ip():
    """
    获取本机IP地址
    """
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"

# 主函数
def main():
    """
    主函数，程序入口点
    """
    args = parse_arguments()
    logger = setup_logging()
    logger.info("AI后端系统启动中...")

    if not check_environment(logger):
        logger.error("环境检查失败，程序退出")
        sys.exit(1)

    # 初始化模型客户端
    try:
        config = get_config()
        default_model_client = OpenAIChatCompletionClient(
            model=config['agents']['default_model']['agent_model_name'],
            api_key=config['agents']['default_model']['agent_model_api_key'],
            model_info={
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "family": "unknown",
                "structured_output": True,
            }
        )
        logger.info("默认模型客户端初始化成功")
    except KeyError as e:
        logger.error(f"默认模型配置缺失: {e}。请检查config.yaml中的'agents.default_model'配置。")
        sys.exit(1)
    except Exception as e:
        logger.error(f"初始化默认模型客户端失败: {e}")
        sys.exit(1)

    # 初始化记忆管理器
    memory_manager = TeachingMemoryManager()
    logger.info("TeachingMemoryManager 初始化成功")

    # 初始化各个 Agent
    crawler_agent = CrawlerEngineerAgent(model_client=default_model_client)
    logger.info("CrawlerEngineerAgent 初始化成功")

    teaching_assistant_agent = TeachingAssistantAgent(
        model_client=default_model_client,
        crawler_agent=crawler_agent,
        memory_manager=memory_manager
    )
    logger.info("TeachingAssistantAgent 初始化成功")

    review_agent = ReviewAgent(
        model_client=default_model_client,
        memory_manager=memory_manager
    )
    logger.info("ReviewAgent 初始化成功")

    # 创建 SelectorGroupChat
    # SelectorGroupChat 负责根据消息内容和Agent能力选择合适的Agent
    # 这里的agents列表定义了参与协作的Agent
    # select_speaker_messages_template 可以自定义选择Agent的提示
    # 创建 SelectorGroupChat
    # 根据官方示例，SelectorGroupChat 直接管理参与者和对话流程
    termination_condition = TextMentionTermination("TERMINATE") # 定义终止条件
    group_chat = SelectorGroupChat(
        participants=[teaching_assistant_agent, review_agent, crawler_agent],
        model_client=default_model_client,
        termination_condition=termination_condition,
        selector_prompt="""
        你是一个智能路由协调员。根据当前的对话内容和以下 Agent 的职责描述，选择最合适的 Agent 来进行下一步操作。

        **核心交互流程**:
        1.  初始用户请求通常首先由 **`TeachingAssistantAgent`** 处理。
        2.  `TeachingAssistantAgent` (TA) 会首先检查其记忆。如果需要外部信息（例如洛谷题目详情），它会：
            a.  构建完整的URL和抓取参数。
            b.  生成一条明确的请求消息给 `@CrawlerEngineerAgent` (CEA)，格式如：`@CrawlerEngineerAgent 请为我抓取题目信息。URL: [完整URL]，内容类型: [content_type]，解析类型: [parse_type]`。
            c.  这条请求消息会作为 TA 的回复。
        3.  当 TA 发出上述抓取请求消息后，你应该选择 **`CrawlerEngineerAgent`**。
        4.  `CrawlerEngineerAgent` (CEA) 执行抓取后，会回复一条包含抓取结果（JSON数据）或错误信息的消息给 TA，格式如：`@TeachingAssistantAgent CrawledData: [JSON_RESULT_STRING]` 或 `@TeachingAssistantAgent CrawlError: [Error Message]`。
        5.  当 CEA 发出上述数据或错误消息后，你应该选择 **`TeachingAssistantAgent`** (TA)。TA 会解析数据（或处理错误）并生成教学内容。
        6.  当 `TeachingAssistantAgent` (TA) 生成了教学内容（无论是初次还是修改后）作为其回复时，你应该选择 **`ReviewAgent`** (RA) 进行审查。
        7.  `ReviewAgent` (RA) 会回复：
            *   `"APPROVED TERMINATE"`: 如果满意，对话结束。
            *   `"REJECTED"` 并附带修改建议。
        8.  如果 `ReviewAgent` (RA) 的回复是 `"REJECTED"`：
            a.  你应该选择 **`TeachingAssistantAgent`** (TA) 来处理修改建议并生成新的教学回复。
            b.  TA 修改完成后，其回复将再次进入流程的第 6 步（即再次由 RA 审查）。

        可用 Agent 及其职责：

        1.  **`TeachingAssistantAgent`**:
            *   **主要职责**: 理解用户C++编程问题，管理与用户的对话流程。
            *   **记忆交互**: 访问记忆系统获取上下文。
            *   **信息获取协调**: 如果需要外部题目信息（如洛谷题目），它会构建抓取参数并【生成请求消息】给 `@CrawlerEngineerAgent`。它【不直接调用工具】进行抓取。
            *   **内容处理**: 在收到 `@CrawlerEngineerAgent` 返回的抓取数据后，解析并使用这些数据来生成教学回复。
            *   **输出**: 教学回复、澄清问题，或者请求 `@CrawlerEngineerAgent` 抓取信息的特定格式消息。
            *   **适用场景**: 处理用户初始请求；在获取到爬虫数据后生成教学内容；根据 `ReviewAgent` 的反馈修改回复。

        2.  **`CrawlerEngineerAgent`**:
            *   **主要职责**: 响应 `@TeachingAssistantAgent` 发来的包含URL和参数的【消息请求】，执行网络抓取和信息解析。
            *   **输入**: 期望接收来自 `TeachingAssistantAgent` 的明确指令消息，包含 `target_url`, `content_type`, `parse_type`。
            *   **输出**: 【回复一条消息】给 `@TeachingAssistantAgent`，内容是抓取到的数据（JSON字符串格式，以 `@TeachingAssistantAgent CrawledData: ` 开头）或错误信息（以 `@TeachingAssistantAgent CrawlError: ` 开头）。【不直接回复 "TERMINATE"】。
            *   **适用场景**: 当 `TeachingAssistantAgent` 发出明确的抓取请求消息时。

        3.  **`ReviewAgent`**:
            *   **主要职责**: 审查 `TeachingAssistantAgent` 生成的最终教学回复。
            *   **输出**: `"APPROVED TERMINATE"` (如果满意，这将结束整个对话) 或 `"REJECTED"` 及修改建议。
            *   **适用场景**: 在 `TeachingAssistantAgent` 生成教学回复后。

        当前对话历史:
        {history}

        请仔细分析【最新一条消息】的内容和发送者，以及上述流程和Agent职责，选择下一个最合适的 Agent。仅返回 Agent 的名称。
        """
    )
    logger.info("SelectorGroupChat 初始化成功")

    # 加载Flask应用
    from services.wecom.callback import app as flask_app
    logger.info("Flask应用加载成功")

    # 将 group_chat 实例传递给 Flask 应用，以便在回调中访问
    flask_app.config['GROUP_CHAT_INSTANCE'] = group_chat
    flask_app.config['TEACHING_ASSISTANT_AGENT'] = teaching_assistant_agent # 传递TeachingAssistantAgent
    flask_app.config['REVIEW_AGENT'] = review_agent # 传递ReviewAgent
    flask_app.config['CRAWLER_AGENT'] = crawler_agent # 传递CrawlerEngineerAgent
    flask_app.config['MEMORY_MANAGER'] = memory_manager # 传递MemoryManager

    local_ip = get_local_ip()
    protocol = "https" if args.ssl else "http"
    callback_url = f"{protocol}://{local_ip}:{args.port}/wecom/callback"
    logger.info(f"企业微信回调URL: {callback_url}")
    print(f"\n============================================================")
    print(f"企业微信回调URL: {callback_url}")
    print(f"请在企业微信管理后台配置此URL作为回调地址")
    print(f"============================================================\n")

    if os.environ.get('GUNICORN_CMD_ARGS') is not None:
        logger.info("在生产模式下运行 (Gunicorn)")
    else:
        logger.info(f"在开发模式下运行 (Flask内置服务器), 主机: {args.host}, 端口: {args.port}, 调试模式: {args.debug}")
        ssl_context = 'adhoc' if args.ssl else None
        flask_app.run(
            host=args.host,
            port=args.port,
            debug=args.debug,
            ssl_context=ssl_context
        )

# 解析命令行参数
def parse_arguments():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description='AI后端系统启动脚本')
    parser.add_argument('--host', default='0.0.0.0', help='服务器主机地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5000, help='服务器端口 (默认: 5000)')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--ssl', action='store_true', help='启用SSL (仅开发模式)')
    return parser.parse_args()

# 导出Flask应用实例 (用于Gunicorn)
# 注意：这里需要确保 services.wecom.callback 中的 app 实例是全局可访问的
from services.wecom.callback import app

if __name__ == "__main__":
    main()