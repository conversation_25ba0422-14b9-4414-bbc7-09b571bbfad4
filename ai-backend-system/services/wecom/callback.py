"""
此文件是回调服务的入口，处理企业微信的 GET（URL 验证）和 POST（消息/事件）请求。
它现在将消息传递给在 main.py 中初始化的 SelectorGroupChat 实例进行处理。
"""

from flask import Flask, request, abort, Response
import logging
import base64
import time
import xml.etree.ElementTree as ET
import traceback
import asyncio # 导入 asyncio

from config import get_config
from services.wecom.crypto import aes_decrypt, verify_signature
# from services.wecom.message_handler import handle_message # 不再直接导入和使用

# 创建Flask应用
app = Flask(__name__)

# 配置日志
logger = logging.getLogger(__name__)

# 加载配置
try:
    wecom_config = get_config()['wecom']
    TOKEN = wecom_config['Token']
    ENCODING_AES_KEY = wecom_config['EncodingAESKey']
    CORP_ID = wecom_config['CorpID']
    logger.info("WeChat Work callback configuration loaded successfully")
except Exception as e:
    logger.error(f"Failed to load WeChat Work configuration: {e}")
    logger.error(traceback.format_exc())
    TOKEN = "YOUR_TOKEN"
    ENCODING_AES_KEY = "YOUR_ENCODING_AES_KEY"
    CORP_ID = "YOUR_CORP_ID"

# 用于防止重放攻击的 nonce 缓存
nonce_cache = set()
NONCE_CACHE_TTL = 300  # 5 分钟（单位：秒）

def clean_nonce_cache():
    """
    清理过期的nonce缓存
    """
    current_time = int(time.time())
    expired = [nk for nk in nonce_cache if current_time - int(nk.split(':')[1]) > NONCE_CACHE_TTL]

    if expired:
        logger.debug(f"Cleaning {len(expired)} expired nonces from cache")
        for nk in expired:
            nonce_cache.discard(nk)

def validate_timestamp(timestamp):
    """
    验证时间戳是否有效
    """
    try:
        timestamp_int = int(timestamp)
        current_time = int(time.time())
        time_diff = abs(current_time - timestamp_int)

        if time_diff > NONCE_CACHE_TTL:
            logger.warning(f"Timestamp expired: {timestamp}, diff: {time_diff}s")
            return False

        return True
    except ValueError:
        logger.warning(f"Invalid timestamp format: {timestamp}")
        raise ValueError("Invalid timestamp format")

@app.route('/wecom/callback', methods=['GET'])
def verify_url():
    """
    处理GET请求（URL验证）
    """
    signature = request.args.get('msg_signature', '')
    timestamp = request.args.get('timestamp', '')
    nonce = request.args.get('nonce', '')
    echostr = request.args.get('echostr', '')

    logger.info(f"Received URL verification request: signature={signature}, timestamp={timestamp}, nonce={nonce}")

    if not all([signature, timestamp, nonce, echostr]):
        logger.warning("Missing required parameters")
        abort(400, "Missing parameters")

    if not verify_signature(signature, timestamp, nonce, echostr, TOKEN):
        logger.warning(f"Invalid signature: {signature}")
        abort(403, "Invalid signature")

    try:
        if not validate_timestamp(timestamp):
            abort(403, "Timestamp expired")
    except ValueError:
        abort(400, "Invalid timestamp format")

    try:
        logger.debug("Decrypting echostr")
        key = base64.b64decode(ENCODING_AES_KEY + '=')
        encrypted_text = base64.b64decode(echostr)
        decrypted_text = aes_decrypt(encrypted_text, key, CORP_ID)

        nonce_key = f"{nonce}:{timestamp}"
        nonce_cache.add(nonce_key)
        clean_nonce_cache()

        logger.info("URL verification successful")
        return decrypted_text
    except Exception as e:
        logger.error(f"Failed to decrypt echostr: {e}")
        logger.error(traceback.format_exc())
        abort(500, "Decryption failed")

@app.route('/WW_verify_wKayc7FPhsFkcaIK.txt', methods=['GET'])
def wecom_domain_verification():
    """
    Handles the WeCom domain verification request.
    """
    return Response("wKayc7FPhsFkcaIK", mimetype="text/plain")

@app.route('/wecom/callback', methods=['POST'])
async def receive_message(): # 将函数定义为异步
    """
    处理POST请求（接收消息或事件）
    """
    signature = request.args.get('msg_signature', '')
    timestamp = request.args.get('timestamp', '')
    nonce = request.args.get('nonce', '')

    logger.info(f"Received message: signature={signature}, timestamp={timestamp}, nonce={nonce}")

    if not all([signature, timestamp, nonce]):
        logger.warning("Missing required parameters")
        abort(400, "Missing parameters")

    try:
        if not validate_timestamp(timestamp):
            abort(403, "Timestamp expired")

        nonce_key = f"{nonce}:{timestamp}"
        if nonce_key in nonce_cache:
            logger.warning(f"Potential replay attack detected: {nonce_key}")
            abort(403, "Nonce reused")
    except ValueError:
        abort(400, "Invalid timestamp format")

    try:
        xml_data = request.data.decode('utf-8')
        logger.debug(f"Received XML: {xml_data}")

        root = ET.fromstring(xml_data)
        encrypted_msg = root.find('Encrypt')

        if encrypted_msg is None or not encrypted_msg.text:
            logger.warning("Missing Encrypt field in XML")
            abort(400, "Invalid XML: missing Encrypt field")

        encrypted_msg = encrypted_msg.text
    except Exception as e:
        logger.error(f"Failed to parse XML: {e}")
        logger.error(traceback.format_exc())
        abort(400, "Invalid XML format")

    if not verify_signature(signature, timestamp, nonce, encrypted_msg, TOKEN):
        logger.warning(f"Invalid signature: {signature}")
        abort(403, "Invalid signature")

    try:
        logger.debug("Decrypting message")
        key = base64.b64decode(ENCODING_AES_KEY + '=')
        encrypted_text = base64.b64decode(encrypted_msg)
        decrypted_xml = aes_decrypt(encrypted_text, key, CORP_ID)

        nonce_cache.add(nonce_key)
        clean_nonce_cache()

        # 解析解密后的XML以获取消息内容和用户ID
        msg_root = ET.fromstring(decrypted_xml)
        msg_type = msg_root.find('MsgType').text if msg_root.find('MsgType') is not None else ''
        from_user_id = msg_root.find('FromUserName').text if msg_root.find('FromUserName') is not None else ''
        content = msg_root.find('Content').text if msg_root.find('Content') is not None else ''
        image_url = msg_root.find('PicUrl').text if msg_root.find('PicUrl') is not None else '' # 图片消息URL
        image_media_id = msg_root.find('MediaId').text if msg_root.find('MediaId') is not None else '' # 图片消息MediaId

        logger.info(f"Decrypted message from {from_user_id}, MsgType: {msg_type}, Content: {content[:50]}...")

        # 获取在 main.py 中初始化的 SelectorGroupChat 实例
        group_chat = app.config.get('GROUP_CHAT_INSTANCE')
        teaching_assistant_agent = app.config.get('TEACHING_ASSISTANT_AGENT') # 确保其他必要的Agent仍然被获取

        if not group_chat or not teaching_assistant_agent: # 更新检查，移除 user_proxy_agent
            logger.error("Required Agent instances (GroupChat, TeachingAssistantAgent) not found in Flask app config. Ensure main.py initializes them correctly.")
            return Response("Error: Agent system not fully initialized", status=500)

        # 构建消息，模拟用户发送给 UserProxyAgent
        # 注意：这里简化了图片处理，实际应用中可能需要下载图片并传递给Agent
        message_content = content
        if msg_type == 'image':
            message_content = f"[用户发送了一张图片] {content}" # 标记为图片消息，Agent内部处理
            # 实际图片数据需要通过其他方式获取和传递，这里仅作标记
            # image_data = await download_image(image_url) # 示例：需要实现图片下载逻辑
            # image_type = "image/jpeg" # 示例：需要根据实际图片类型设置

        # 使用 UserProxyAgent 发送消息到 GroupChat
        # GroupChat 会根据消息内容和Agent能力选择合适的Agent进行回复
        # 使用 SelectorGroupChat 的 run_stream 方法来启动对话
        # run_stream 返回一个异步生成器，需要迭代处理或获取最终结果
        final_response_parts = []
        async for reply in group_chat.run_stream(task=message_content):
            # reply 可能是消息对象或字符串，具体取决于 Autogen 版本和配置
            if isinstance(reply, dict) and "content" in reply:
                final_response_parts.append(reply["content"])
            elif isinstance(reply, str):
                final_response_parts.append(reply)
            # 可以根据需要添加更多对 reply 类型的处理
        
        final_response_from_stream = "\n".join(final_response_parts)
        chat_result = {"summary": final_response_from_stream if final_response_from_stream else "抱歉，未能生成有效回复。"}


        # 获取最终回复
        # chat_result is a dict: {"summary": "response_string"}
        final_response = chat_result.get("summary") if chat_result and chat_result.get("summary") else "抱歉，未能生成有效回复。"
        
        logger.info(f"Final response from agents for user {from_user_id}: {final_response[:100]}...")
        return final_response
    except Exception as e:
        logger.error(f"Failed to process message with agents: {e}")
        logger.error(traceback.format_exc())
        abort(500, "Processing failed")

def create_app(test_config=None):
    """
    创建Flask应用的工厂函数
    """
    if test_config:
        app.config.update(test_config)
    return app

# 启动服务器（仅在直接运行此文件时）
if __name__ == '__main__':
    logging.basicConfig(
        filename='ai-backend-system/logs/wecom_callback.log',
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    logger.info("Starting WeChat Work callback server...")

    try:
        # 注意：直接运行此文件时，Agent实例不会被初始化和传递
        # 建议通过 main.py 启动应用以确保Agent系统正常工作
        logger.warning("Running callback.py directly. Agent system will not be fully initialized. Please run main.py instead.")
        app.run(host='0.0.0.0', port=5000, ssl_context='adhoc')
        logger.info("Server started on port 5000 with HTTPS")
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        logger.error(traceback.format_exc())

# 单元测试代码
if __name__ == "__test__":
    import unittest
    from unittest.mock import patch, MagicMock, AsyncMock
    import json

    class CallbackTest(unittest.TestCase):
        def setUp(self):
            self.app = create_app({'TESTING': True})
            self.client = self.app.test_client()
            nonce_cache.clear()
            self.timestamp = str(int(time.time()))
            self.nonce = '1234567890'
            self.token = TOKEN
            self.encoding_aes_key = ENCODING_AES_KEY
            self.corp_id = CORP_ID

            # Mock Agent instances for testing
            self.mock_user_proxy_agent = MagicMock()
            self.mock_user_proxy_agent.a_initiate_chat = AsyncMock(return_value=MagicMock(summary="Mocked Agent Response"))
            self.mock_group_chat = MagicMock()

            self.app.config['GROUP_CHAT_INSTANCE'] = self.mock_group_chat
            self.app.config['USER_PROXY_AGENT'] = self.mock_user_proxy_agent
            self.app.config['TEACHING_ASSISTANT_AGENT'] = MagicMock()
            self.app.config['REVIEW_AGENT'] = MagicMock()
            self.app.config['CRAWLER_AGENT'] = MagicMock()
            self.app.config['MEMORY_MANAGER'] = MagicMock()

        @patch('services.wecom.crypto.verify_signature')
        @patch('services.wecom.crypto.aes_decrypt')
        async def test_verify_url(self, mock_decrypt, mock_verify):
            mock_verify.return_value = True
            mock_decrypt.return_value = 'test_echo_str'
            response = await self.client.get(
                '/wecom/callback',
                query_string={
                    'msg_signature': 'test_signature',
                    'timestamp': self.timestamp,
                    'nonce': self.nonce,
                    'echostr': 'test_encrypted_str'
                }
            )
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.data.decode('utf-8'), 'test_echo_str')
            self.assertIn(f"{self.nonce}:{self.timestamp}", nonce_cache)

        @patch('services.wecom.crypto.verify_signature')
        @patch('services.wecom.crypto.aes_decrypt')
        async def test_receive_message(self, mock_decrypt, mock_verify):
            mock_verify.return_value = True
            mock_decrypt.return_value = '<xml><MsgType>text</MsgType><FromUserName>test_user</FromUserName><Content>Hello</Content></xml>'
            xml_body = '<xml><Encrypt>test_encrypted_msg</Encrypt></xml>'
            response = await self.client.post(
                '/wecom/callback',
                query_string={
                    'msg_signature': 'test_signature',
                    'timestamp': self.timestamp,
                    'nonce': self.nonce
                },
                data=xml_body,
                content_type='text/xml'
            )
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.data.decode('utf-8'), 'Mocked Agent Response')
            self.mock_user_proxy_agent.a_initiate_chat.assert_called_once_with(
                self.mock_group_chat,
                message="Hello",
                sender="test_user",
                silent=False
            )

        @patch('services.wecom.crypto.verify_signature')
        @patch('services.wecom.crypto.aes_decrypt')
        async def test_receive_image_message(self, mock_decrypt, mock_verify):
            mock_verify.return_value = True
            mock_decrypt.return_value = '<xml><MsgType>image</MsgType><FromUserName>test_user_img</FromUserName><PicUrl>http://example.com/img.jpg</PicUrl><MediaId>media123</MediaId><Content>Image description</Content></xml>'
            xml_body = '<xml><Encrypt>test_encrypted_img_msg</Encrypt></xml>'
            response = await self.client.post(
                '/wecom/callback',
                query_string={
                    'msg_signature': 'test_signature',
                    'timestamp': self.timestamp,
                    'nonce': self.nonce
                },
                data=xml_body,
                content_type='text/xml'
            )
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.data.decode('utf-8'), 'Mocked Agent Response')
            self.mock_user_proxy_agent.a_initiate_chat.assert_called_once_with(
                self.mock_group_chat,
                message="[用户发送了一张图片] Image description",
                sender="test_user_img",
                silent=False
            )

        async def test_replay_attack_detection(self):
            nonce_key = f"{self.nonce}:{self.timestamp}"
            nonce_cache.add(nonce_key)
            xml_body = '<xml><Encrypt>test_encrypted_msg</Encrypt></xml>'
            response = await self.client.post(
                '/wecom/callback',
                query_string={
                    'msg_signature': 'test_signature',
                    'timestamp': self.timestamp,
                    'nonce': self.nonce
                },
                data=xml_body,
                content_type='text/xml'
            )
            self.assertEqual(response.status_code, 403)

        async def test_missing_parameters(self):
            response = await self.client.get('/wecom/callback')
            self.assertEqual(response.status_code, 400)
            response = await self.client.post('/wecom/callback')
            self.assertEqual(response.status_code, 400)

        async def test_invalid_xml(self):
            response = await self.client.post(
                '/wecom/callback',
                query_string={
                    'msg_signature': 'test_signature',
                    'timestamp': self.timestamp,
                    'nonce': self.nonce
                },
                data='invalid xml',
                content_type='text/xml'
            )
            self.assertEqual(response.status_code, 400)

            response = await self.client.post(
                '/wecom/callback',
                query_string={
                    'msg_signature': 'test_signature',
                    'timestamp': self.timestamp,
                    'nonce': self.nonce
                },
                data='<xml><NotEncrypt>test</NotEncrypt></xml>',
                content_type='text/xml'
            )
            self.assertEqual(response.status_code, 400)

    # 运行单元测试
    if __name__ == "__main__": # Changed from "__test__" to "__main__" for direct execution
        unittest.main(argv=['first-arg-is-ignored'], exit=False)