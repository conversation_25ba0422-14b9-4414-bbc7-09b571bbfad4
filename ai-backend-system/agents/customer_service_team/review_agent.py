"""
定义审查代理 (ReviewAgent) 类，该类继承自 AssistantAgent。

该 Agent 负责：
- 接收 TeachingAssistantAgent 生成的回复。
- 基于用户请求和记忆上下文，对回复进行准确性、完整性、格式合规性以及教学质量的审查。
- 通过 TeachingMemoryManager 对用户记忆进行只读访问。
- 提供审查反馈（通过或修改建议）。
"""

import logging
from typing import Optional, Dict, Any

from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core import CancellationToken
from autogen_core.models import SystemMessage, UserMessage

from config import get_config
from agents.customer_service_team.teaching_memory_manager import TeachingMemoryManager

logger = logging.getLogger(__name__)

class ReviewAgent(AssistantAgent):
    """
    审查代理，负责审查教学助手生成的回复。
    """

    def __init__(
        self,
        name: str = "review_agent",
        system_message: str = """你是一位严谨的教育内容审查专家。你的任务是审查教学助手（TeachingAssistantAgent）生成的C++编程问题回复。

你的审查原则：
1.  **准确性**: 回复中的C++概念、代码示例和解题思路是否完全正确，没有误导性信息。
2.  **完整性**: 回复是否充分解答了用户的问题，是否遗漏了关键信息或步骤。
3.  **格式合规性**: 回复是否严格遵循了纯文本格式要求（无Markdown代码块、无粗体斜体、无链接格式，使用缩进和空行展示代码，使用数字和字母编号）。
4.  **专业性与语气**: 回复是否使用专业、严谨的语气，避免过度使用emoji或儿童化语言。
5.  **连贯性**: 回复是否与用户的历史对话和学习上下文保持连贯。
6.  **简洁性**: 回复是否在满足要求的前提下尽可能简洁，避免冗余信息，并确保字节长度不超过2048字节。

审查反馈格式要求：
-   如果回复完全符合要求，请回复 "APPROVED"。
-   如果回复需要修改，请回复 "REJECTED"，并在其后详细说明需要修改的具体点和理由，例如：
    "REJECTED:
    1.  准确性问题：代码示例中的变量名有误。
    2.  完整性问题：未解释指针的概念。
    3.  格式问题：使用了Markdown粗体标记。
    4.  长度问题：回复超过了2048字节限制，请精简。"
""",
        model_client: Optional[OpenAIChatCompletionClient] = None,
        memory_manager: Optional[TeachingMemoryManager] = None,
    ) -> None:
        """
        初始化审查代理。

        Args:
            name: 代理名称。
            system_message: 系统提示消息。
            model_client: 模型客户端，默认使用配置中指定的模型。
            memory_manager: 教学记忆管理器实例，用于只读访问用户记忆。
        """
        if model_client is None:
            try:
                config = get_config()
                model_client = OpenAIChatCompletionClient(
                    model=config['agents']['review_agent']['agent_model_name'],
                    api_key=config['agents']['review_agent']['agent_model_api_key'],
                    model_info={
                        "vision": False,
                        "function_calling": True,
                        "json_output": True,
                        "family": "unknown",
                        "structured_output": True,
                    }
                )
            except KeyError as e:
                logger.info(f"Configuration key not found in ReviewAgent model client initialization: {e}, using default values")
                model_client = OpenAIChatCompletionClient(
                    model="gemini-2.0-flash",
                    api_key=get_config()['api_keys']['gemini_api_key'],
                    model_info={
                        "vision": False,
                        "function_calling": True,
                        "json_output": True,
                        "family": "unknown",
                        "structured_output": True,
                    }
                )
        self.model_client = model_client
        self.memory_manager = memory_manager if memory_manager else TeachingMemoryManager()

        super().__init__(
            name=name,
            system_message=system_message,
            model_client=model_client,
            memory=[], # ReviewAgent 不直接管理记忆，通过 memory_manager 访问
            reflect_on_tool_use=False # ReviewAgent 主要进行评估，不直接使用工具
        )
        logger.info(f"ReviewAgent initialized with name: {name}")

    async def review_response(
        self,
        user_id: str,
        original_user_message: str,
        teaching_agent_response: str,
        cancellation_token: Optional[CancellationToken] = None
    ) -> str:
        """
        审查教学助手生成的回复。

        Args:
            user_id: 用户ID。
            original_user_message: 用户的原始问题。
            teaching_agent_response: 教学助手生成的回复。
            cancellation_token: 取消令牌。

        Returns:
            str: 审查结果 ("APPROVED" 或 "REJECTED: ...")。
        """
        logger.info(f"Reviewing response for user {user_id}. Original message: {original_user_message[:50]}...")
        logger.info(f"Teaching Agent's response to review: {teaching_agent_response[:100]}...")

        # 1. 从记忆管理器获取相关记忆（只读访问）
        # ReviewAgent 需要用户的历史对话和学习上下文来评估回复的连贯性和相关性
        relevant_memories = await self.memory_manager.retrieve_relevant_memories(
            user_id=user_id,
            current_user_message=original_user_message,
            cancellation_token=cancellation_token
        )

        # 构建审查提示
        review_prompt = self._build_review_prompt(
            original_user_message,
            teaching_agent_response,
            relevant_memories
        )

        # 调用LLM进行审查
        messages = [
            SystemMessage(content=self.system_message),
            UserMessage(content=review_prompt, source="user")
        ]

        try:
            if hasattr(self.model_client, 'create'):
                review_result = await self.model_client.create(messages=messages, cancellation_token=cancellation_token)
            elif hasattr(self.model_client, 'complete'):
                review_result = await self.model_client.complete(messages=messages, cancellation_token=cancellation_token)
            elif hasattr(self.model_client, 'chat_completion'):
                review_result = await self.model_client.chat_completion(messages=messages, cancellation_token=cancellation_token)
            else:
                review_result = await self.model_client(messages=messages, cancellation_token=cancellation_token)

            if not review_result or not review_result.content:
                logger.warning(f"Empty review result from LLM for user {user_id}")
                return "REJECTED: 审查代理未能生成有效审查结果。"

            review_content = review_result.content
            logger.info(f"Review result for user {user_id}: {review_content}")
            if review_content.strip().upper() == "APPROVED":
                return "APPROVED TERMINATE"
            return review_content
        except Exception as e:
            logger.error(f"Error during review process for user {user_id}: {str(e)}")
            return f"REJECTED: 审查过程中发生错误：{str(e)}"

    def _build_review_prompt(
        self,
        original_user_message: str,
        teaching_agent_response: str,
        relevant_memories: Dict[str, Any]
    ) -> str:
        """
        构建审查提示。

        Args:
            original_user_message: 用户的原始问题。
            teaching_agent_response: 教学助手生成的回复。
            relevant_memories: 从记忆管理器获取的相关记忆。

        Returns:
            str: 构建的审查提示。
        """
        prompt = f"请审查以下教学助手对C++编程问题的回复。请严格按照你的审查原则进行评估。\n\n"
        prompt += f"原始用户问题：\n{original_user_message}\n\n"
        prompt += f"教学助手回复：\n{teaching_agent_response}\n\n"

        if relevant_memories:
            prompt += "以下是与用户相关的记忆上下文（只读，用于评估回复的连贯性和相关性）：\n"
            if relevant_memories.get("recent_conversation"):
                prompt += "最近的对话历史：\n"
                for i, mem_obj in enumerate(relevant_memories["recent_conversation"][:5]): # 限制数量
                    content = str(mem_obj.content)
                    role = mem_obj.metadata.get('role', 'unknown')
                    prompt += f"{i+1}. [{role}] {content[:100]}...\n" # 限制长度
                prompt += "\n"

            if relevant_memories.get("topic_related"):
                prompt += "与当前问题主题相关的记忆：\n"
                for i, mem_obj in enumerate(relevant_memories["topic_related"][:3]): # 限制数量
                    content = str(mem_obj.content)
                    prompt += f"{i+1}. {content[:100]}...\n"
                prompt += "\n"

            if relevant_memories.get("concept_related"):
                prompt += "与当前问题概念相关的记忆：\n"
                for i, mem_obj in enumerate(relevant_memories["concept_related"][:3]): # 限制数量
                    content = str(mem_obj.content)
                    prompt += f"{i+1}. {content[:100]}...\n"
                prompt += "\n"
        
        prompt += "请根据上述信息和你的审查原则，给出审查结果（'APPROVED' 或 'REJECTED: ...'）。"
        logger.debug(f"Review prompt built: {prompt[:500]}...")
        return prompt

# 单元测试代码
if __name__ == "__main__":
    import unittest
    from unittest.mock import patch, MagicMock, AsyncMock
    import asyncio

    class ReviewAgentTest(unittest.TestCase):
        @patch('autogen_ext.models.openai.OpenAIChatCompletionClient')
        @patch('agents.customer_service_team.teaching_memory_manager.TeachingMemoryManager')
        @patch('config.get_config')
        def setUp(self, mock_get_config, mock_memory_manager, mock_openai_client):
            # Mock get_config
            mock_get_config.return_value = {
                'agents': {
                    'review_agent': {
                        'agent_model_name': 'test-review-model',
                        'agent_model_api_key': 'test-review-key',
                    }
                },
                'api_keys': {
                    'gemini_api_key': 'gemini-test-key'
                }
            }

            # Mock OpenAIChatCompletionClient
            self.mock_llm_client = mock_openai_client.return_value
            self.mock_llm_client.create = AsyncMock(return_value=MagicMock(content="APPROVED"))

            # Mock TeachingMemoryManager
            self.mock_memory_manager = mock_memory_manager.return_value
            self.mock_memory_manager.retrieve_relevant_memories = AsyncMock(return_value={
                "recent_conversation": [
                    MagicMock(content="[user] Previous question.", metadata={'role': 'user'}),
                    MagicMock(content="[assistant] Previous answer.", metadata={'role': 'assistant'})
                ],
                "topic_related": [],
                "concept_related": []
            })

            self.agent = ReviewAgent(
                model_client=self.mock_llm_client,
                memory_manager=self.mock_memory_manager
            )

        async def test_review_response_approved(self):
            user_id = "test_user_1"
            original_message = "How to implement quicksort?"
            teaching_response = "Quicksort is a divide-and-conquer algorithm. Here's a simple explanation..."

            result = await self.agent.review_response(user_id, original_message, teaching_response)
            self.assertEqual(result, "APPROVED")
            self.mock_memory_manager.retrieve_relevant_memories.assert_called_once_with(
                user_id=user_id,
                current_user_message=original_message,
                cancellation_token=None
            )
            self.mock_llm_client.create.assert_called_once()
            args, _ = self.mock_llm_client.create.call_args
            prompt_message = args[0][1].content # UserMessage content
            self.assertIn(original_message, prompt_message)
            self.assertIn(teaching_response, prompt_message)
            self.assertIn("Previous question.", prompt_message)

        async def test_review_response_rejected(self):
            self.mock_llm_client.create.return_value = MagicMock(content="REJECTED: 格式问题：使用了Markdown粗体标记。")
            user_id = "test_user_2"
            original_message = "What is a pointer?"
            teaching_response = "A **pointer** is a variable that stores the memory address..."

            result = await self.agent.review_response(user_id, original_message, teaching_response)
            self.assertIn("REJECTED", result)
            self.assertIn("格式问题：使用了Markdown粗体标记。", result)

        async def test_review_response_llm_error(self):
            self.mock_llm_client.create.side_effect = Exception("LLM API error")
            user_id = "test_user_3"
            original_message = "Explain recursion."
            teaching_response = "Recursion is a process where a function calls itself."

            result = await self.agent.review_response(user_id, original_message, teaching_response)
            self.assertIn("REJECTED", result)
            self.assertIn("审查过程中发生错误：LLM API error", result)

    # 运行单元测试
    if __name__ == "__main__":
        unittest.main(argv=['first-arg-is-ignored'], exit=False)