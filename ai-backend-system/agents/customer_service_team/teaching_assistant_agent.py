"""
定义教学助手 Agent (TeachingAssistantAgent) 类,该类继承自 AssistantAgent.

该 Agent 负责:
- 接收用户在企业微信 (WeCom) 上发送的关于 AI 教学的问题.
- 调用 LLM 模型 (通过配置的 LLM API) 生成 AI 教学内容以及回复学生问题.
- 通过 TeachingMemoryManager 管理用户记忆,实现记忆的读写.
- 将生成的教学内容回复给用户.

核心业务流程包括:
- TeachingAssistantAgent接收来自services/wecom/message_handler.py的用户问题,范围仅限于python编程竞赛题目答疑.
- TeachingAssistantAgent会根据用户问题,判断是否需要直接解答或需要调用agents/dev_team/crawler_engineer_agent.py进行相关题目抓取后解答.
- 对于需要调用crawler engineer agent的情况,crawler engineer agent会调用crawler tool抓取相关题目信息,然后将结果返回给TeachingAssistantAgent.对于需要抓取的内容,目前仅限于洛谷python编程题目,比如题号类似:P1005,P1560等.
- TeachingAssistantAgent与ReviewAgent,CrawlerEngineerAgent通过SelectorGroupChat协作.
- TeachingAssistantAgent可以更新和检索记忆,而ReviewAgent对记忆进行只读访问.
"""

import logging
import re
import os
import sys
import asyncio
import time
from datetime import datetime
from typing import Optional, Dict, Any, List

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.tools import AgentTool
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core import CancellationToken
from autogen_core.models import SystemMessage, UserMessage

# 使用相对导入以适应不同的Python路径设置
from config import get_config
from data_models import ContentType
from agents.dev_team.crawler_engineer_agent import CrawlerEngineerAgent, ParseType
from agents.customer_service_team.teaching_memory_manager import TeachingMemoryManager
from agents.customer_service_team.review_agent import ReviewAgent # 导入ReviewAgent

# 配置日志
logger = logging.getLogger(__name__)

class TeachingAssistantAgent(AssistantAgent):
    """教学助手代理,负责回答学生关于C++编程竞赛题目的问题

    特点:
    1. 通过TeachingMemoryManager管理用户记忆,实现解耦
    2. 支持从配置文件加载LLM模型配置
    3. 能够与CrawlerEngineerAgent协作抓取题目信息
    4. 能够与ReviewAgent协作审查回复质量
    5. 提供详细的日志记录,便于调试和监控
    """

    def __init__(
        self,
        name: str = "teaching_assistant",
        system_message: str = """你是一位C++编程教学助手.你的主要职责是理解学生的编程问题(目前专注于C++和洛谷题目),并提供清晰,准确的解答.

核心工作流程:
1.  **接收问题**:从学生处接收编程问题.
2.  **记忆检索**:首先,你会利用记忆系统(TeachingMemoryManager)检查短期记忆中是否已包含该用户关于此问题或相关题目的信息.
3.  **判断是否需要外部信息并准备抓取请求**:
    *   如果学生提到特定的题目编号(例如 "P1005"),并且你判断记忆中的信息不足以解答,你需要从外部网站(如洛谷)获取题目详情.
    *   你将根据配置的题目来源基础URL和题目ID,【构建完整的题目URL】.
    *   然后,你将【生成一条明确的请求消息】给 `CrawlerEngineerAgent`.这条消息必须包含:
        *   明确提及 `@CrawlerEngineerAgent` 以确保消息被正确路由.
        *   需要抓取的完整 `target_url` (由你根据题目ID和配置的基础URL构建).
        *   `content_type` (对于洛谷题目,通常是 "competition_topic").
        *   `parse_type` (对于洛谷题目详情,通常是 "item_detail").
    *   **示例请求消息格式**:`@CrawlerEngineerAgent 请为我抓取题目信息.URL: [这里是你构建的完整URL],内容类型: competition_topic,解析类型: item_detail`
    *   在生成此请求消息后,你的当前任务是等待 `CrawlerEngineerAgent` 的回复.你不会直接进行网络抓取.
4.  **处理抓取结果**:
    *   当 `CrawlerEngineerAgent` 回复抓取到的题目信息(通常是JSON格式的字符串)后,你需要解析这些信息.
5.  **整合信息与生成教学回复**:结合记忆中的信息,以及从 `CrawlerEngineerAgent` 获取并解析后的题目信息,生成最终的教学回复.
    *   如果用户请求宽泛(例如仅提供题目ID),且题目信息已成功获取,请进行系统性的,完整的讲解.
6.  **回复原则与格式**:
    *   简洁明了,不做无关扩展.
    *   确保回复不超过2048字节(约600-700中文字).
    *   使用正常,专业的语气.
    *   解释概念时使用清晰的比喻,但保持专业性.
    *   提供简单易懂的代码示例.
    *   不使用Markdown格式,代码直接使用缩进和空行.
    *   使用数字和字母编号组织内容,空行分隔段落.

记住:你的目标是提供专业,清晰的指导.协调信息获取(记忆检索,准备抓取请求给CrawlerEngineerAgent)是你的关键职责之一.""",
        model_client: Optional[OpenAIChatCompletionClient] = None,
        crawler_agent: Optional[CrawlerEngineerAgent] = None,
        memory_manager: Optional[TeachingMemoryManager] = None,
    ) -> None:
        """初始化教学助手代理

        Args:
            name: 代理名称
            system_message: 系统提示消息
            model_client: 模型客户端,默认使用配置中指定的模型
            crawler_agent: 爬虫工程师代理,用于抓取题目信息
            memory_manager: 教学记忆管理器实例
        """
        if model_client is None:
            config = get_config() # Load config once
            try:
                ta_config = config['agents']['teaching_assistant']
                model_client = OpenAIChatCompletionClient(
                    model=ta_config['agent_model_name'],
                    api_key=ta_config['agent_model_api_key'],
                    model_info={
                        "vision": False,
                        "function_calling": True,
                        "json_output": True,
                        "family": "unknown",
                        "structured_output": True,
                    }
                )
                # Load problem URL bases from config
                self.problem_url_bases = ta_config.get('problem_url_bases', {})
                if not self.problem_url_bases:
                    logger.warning("Problem URL bases not found in config for TeachingAssistantAgent. Crawler functionality might be limited.")
                    # Provide a default fallback if not found in config
                    self.problem_url_bases = {"luogu": "https://www.luogu.com.cn/problem/"}


            except KeyError as e:
                logger.info(f"Configuration key not found in model client or problem_url_bases initialization: {e}, using default values for model and Luogu URL.")
                model_client = OpenAIChatCompletionClient(
                    model="gemini-2.5-flash-preview-05-20", # Ensure this matches your confirmed model
                    api_key=config['api_keys']['gemini_api_key'],
                    model_info={
                        "vision": False,
                        "function_calling": True,
                        "json_output": True,
                        "family": "unknown",
                        "structured_output": True,
                    }
                )
                self.problem_url_bases = {"luogu": "https://www.luogu.com.cn/problem/"} # Default fallback

        self.model_client = model_client
        self.crawler_agent = crawler_agent if crawler_agent else CrawlerEngineerAgent()
        self.memory_manager = memory_manager if memory_manager else TeachingMemoryManager()
        
        # crawler_agent is a participant in SelectorGroupChat, not a direct tool of TeachingAssistantAgent.
        # Interaction will be via messages routed by SelectorGroupChat.
        # self.crawler_agent instance is kept if needed for other purposes, but not registered as a tool here.
        # Note: The 'crawler_agent' parameter in __init__ is now less critical for the core message-based flow,
        # but retained for consistency or if other direct utility functions from it were ever needed.

        super().__init__(
            name=name,
            system_message=system_message,
            model_client=model_client,
            tools=None, # No direct tools for calling other group chat participants
            memory=[],
            reflect_on_tool_use=False # Tool use reflection is not relevant if not using tools this way
        )
        logger.info(f"TeachingAssistantAgent initialized with name: {name}")

    async def handle_user_message(self, user_id: str, content: str, image_data: Optional[bytes] = None,
                                 image_type: Optional[str] = None, cancellation_token: Optional[CancellationToken] = None) -> str:
        """处理用户消息并生成回复.此方法现在管理多轮交互的可能状态."""
        logger.info(f"Handling message for user {user_id}. Incoming content: {content[:100]}...")

        # --- 图片消息处理 ---
        if image_data:
            try:
                await self._process_image(image_data, image_type)
                if "[用户发送了一张图片]" not in content: # Ensure marker is added
                    content = f"{content}\n\n[用户发送了一张图片]" if content.strip() else "[用户发送了一张图片]"
            except Exception as e:
                 logger.error(f"Failed to process image in handle_user_message: {e}")
                 content = f"{content}\n\n[处理图片时出错]" if content.strip() else "[处理图片时出错]"

        is_image_message_marker = "[用户发送了一张图片]" in content
        is_just_image_prompt = is_image_message_marker and \
                               len(content.replace("[用户发送了一张图片]", "").strip()) < 10 and \
                               not any(kw in content.lower() for kw in ["p1", "p2", "p3", "p4", "p5", "p6", "p7", "p8", "p9", "题", "问题"])
        
        if is_just_image_prompt:
            logger.info(f"Image-only message detected from user {user_id}, returning standard image prompt.")
            response_text = "我看到您发送了一张图片,但我无法直接查看图片内容.请您简要描述一下图片中的内容,比如这是一道什么题目,或者您想问什么问题,这样我才能更好地帮助您."
            await self.memory_manager.add_message(user_id, content, "user", cancellation_token)
            await self.memory_manager.add_message(user_id, response_text, "assistant", cancellation_token)
            return response_text

        # --- 核心逻辑:区分用户初始请求 vs 处理爬虫回复 ---
        
        # 1. 将当前收到的消息存入记忆
        # Note: If content is from crawler, its role might ideally be different.
        # For now, all incoming 'content' is added as "user" role from TA's perspective of receiving a message.
        await self.memory_manager.add_message(user_id, content, "user", cancellation_token)

        # 2. 检索相关记忆 (基于当前 content, which could be user query or crawler response)
        # This memory retrieval happens *after* adding the current message.
        relevant_memories = await self.memory_manager.retrieve_relevant_memories(
            user_id=user_id,
            current_user_message=content,
            cancellation_token=cancellation_token
        )

        # 3. 尝试确定消息类型和准备LLM输入
        parsed_problem_data_from_crawl = None
        user_message_for_llm = content
        crawl_request_params_for_llm = None
        
        CRAWLER_RESPONSE_PREFIX = "@TeachingAssistantAgent CrawledData: "

        if content.strip().startswith(CRAWLER_RESPONSE_PREFIX):
            logger.info(f"Message content for user {user_id} is being treated as crawled data.")
            json_str_part = content.split(CRAWLER_RESPONSE_PREFIX, 1)[1].strip()
            try:
                potential_data_list = json.loads(json_str_part)
                if isinstance(potential_data_list, list) and len(potential_data_list) > 0 and \
                   isinstance(potential_data_list[0], dict) and "problem_id" in potential_data_list[0]:
                    parsed_problem_data_from_crawl = potential_data_list[0]
                    logger.info(f"Successfully parsed crawled data for problem: {parsed_problem_data_from_crawl.get('problem_id')}")
                    
                    original_query_found = False
                    # Look into memory (which now includes the crawler's response as the last "user" message)
                    if relevant_memories and relevant_memories.get("recent_conversation"):
                        # Iterate backwards, skipping the last message (crawler's response)
                        for mem_idx in range(len(relevant_memories["recent_conversation"]) - 2, -1, -1):
                            mem_item = relevant_memories["recent_conversation"][mem_idx]
                            if hasattr(mem_item, 'metadata') and mem_item.metadata.get('role') == 'user':
                                if not (isinstance(mem_item.content, str) and mem_item.content.startswith("@CrawlerEngineerAgent")):
                                    user_message_for_llm = str(mem_item.content)
                                    original_query_found = True
                                    logger.info(f"Retrieved original user query for this crawl cycle: {user_message_for_llm[:50]}...")
                                    break
                    if not original_query_found:
                        logger.warning(f"Could not retrieve original user query for user {user_id} after crawl. Using generic prompt based on crawled data.")
                        pid = parsed_problem_data_from_crawl.get('problem_id', '未知题目') if parsed_problem_data_from_crawl else '未知题目'
                        user_message_for_llm = f"请详细讲解题目 {pid}"
                        if parsed_problem_data_from_crawl and parsed_problem_data_from_crawl.get("error"):
                             user_message_for_llm = f"获取题目 {pid} 信息时出错:{parsed_problem_data_from_crawl.get('error')}.请告知用户."

                else:
                    logger.warning(f"Crawled data from CEA not in expected list/dict format: {json_str_part}")
                    parsed_problem_data_from_crawl = {"error": "Crawled data format error from CEA."}
                    user_message_for_llm = "处理获取的题目信息时遇到格式问题,请告知用户."
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON from CEA response: {e}. Content: {json_str_part}")
                parsed_problem_data_from_crawl = {"error": f"Invalid JSON data from crawler: {str(e)}"}
                user_message_for_llm = "处理获取的题目信息时遇到解析错误,请告知用户."
            except Exception as e:
                logger.warning(f"Unexpected error processing CEA response: {e}. Content: {content}")
                parsed_problem_data_from_crawl = {"error": f"Unexpected error processing crawler data: {str(e)}"}
                user_message_for_llm = "处理获取的题目信息时发生未知错误,请告知用户."
        
        else: # Not a crawl response, so it's a direct user message.
            user_message_for_llm = content
            problem_id = self._extract_problem_id(user_message_for_llm)
            if problem_id:
                # Check memory for existing info (simplified for now)
                # We always prepare params; LLM in _build_response_prompt decides if crawl request is needed.
                logger.info(f"Detected problem ID: {problem_id} in user message. Preparing crawl request parameters.")
                crawl_request_params_for_llm = await self._prepare_crawl_request_params(problem_id)
        
        response_text = await self._generate_response(
            user_id,
            user_message_for_llm,    # The message LLM should focus on
            parsed_problem_data_from_crawl, # Parsed data from crawler (if any), or None
            relevant_memories,
            cancellation_token,
            crawl_request_params=crawl_request_params_for_llm
        )

        # 将 TA 的最终回复添加到记忆
        await self.memory_manager.add_message(user_id, response_text, "assistant", cancellation_token)
        return response_text

    async def _process_image(self, image_data: bytes, image_type: str) -> str:
        """处理图片数据,提取图片中的文本或其他内容"""
        try:
            import tempfile
            import os

            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{image_type.split('/')[-1] if '/' in image_type else 'jpg'}") as temp_file:
                temp_file.write(image_data)
                temp_file_path = temp_file.name

            try:
                ocr_result = "我看到您发送了一张图片.由于目前我无法直接分析图片内容,请您简要描述一下图片中的内容,比如这是一道什么题目,或者您想问什么问题,这样我才能更好地帮助您."
                os.unlink(temp_file_path)
                return ocr_result
            except Exception as e:
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                raise e
        except Exception as e:
            logger.error(f"Error processing image: {str(e)}")
            return "我在处理您发送的图片时遇到了一些问题.请您描述一下图片中的内容,或者直接输入您的问题,这样我就能更好地帮助您了."

    def _extract_problem_id(self, content: str) -> Optional[str]:
        """从用户消息中提取题目编号"""
        pattern = r'P\d{4,}'
        match = re.search(pattern, content)
        if match:
            return match.group(0)
        return None

    def _extract_problem_id_from_memory(self, memory_results: Any) -> Optional[str]:
        """从记忆结果中提取题目编号"""
        if not memory_results or not memory_results.get('recent_conversation'):
            return None

        for memory_item in memory_results['recent_conversation']:
            if hasattr(memory_item, 'content'):
                content = str(memory_item.content)
                problem_id = self._extract_problem_id(content)
                if problem_id:
                    return problem_id
        return None

    async def _extract_recent_problem_info(self, user_id: str, memory_results: Dict[str, List[Any]]) -> Optional[Dict[str, Any]]:
        """从记忆中提取最近讨论的题目信息"""
        try:
            if memory_results and memory_results.get('recent_conversation'):
                for memory_item in memory_results['recent_conversation']:
                    if hasattr(memory_item, 'content'):
                        content = str(memory_item.content)
                        problem_id = self._extract_problem_id(content)
                        if problem_id:
                            logger.info(f"Found problem ID {problem_id} in memory for user {user_id}")
                            if "标题" in content or "title" in content.lower() or "描述" in content or "description" in content.lower():
                                problem_info = {"problem_id": problem_id}
                                title_match = re.search(r'标题[::]\s*(.+?)(?:\n|$)', content)
                                if title_match:
                                    problem_info["title"] = title_match.group(1).strip()
                                desc_match = re.search(r'描述[::]\s*(.+?)(?:\n|$)', content)
                                if desc_match:
                                    problem_info["description"] = desc_match.group(1).strip()
                                if len(problem_info) > 1:
                                    return problem_info

            problem_id = self._extract_problem_id_from_memory(memory_results)
            if not problem_id:
                return None

            problem_query = f"题目 {problem_id} 的详细信息"
            problem_memories = await self.memory_manager.query_memories(
                query=problem_query,
                user_id=user_id,
                cancellation_token=None
            )

            if not problem_memories or not hasattr(problem_memories, 'results') or not problem_memories.results:
                return {"problem_id": problem_id}

            problem_info = {"problem_id": problem_id}
            for memory_item in problem_memories.results:
                if hasattr(memory_item, 'content'):
                    content = str(memory_item.content)
                    title_match = re.search(r'标题[::]\s*(.+?)(?:\n|$)', content)
                    if title_match and "title" not in problem_info:
                        problem_info["title"] = title_match.group(1).strip()
                    desc_match = re.search(r'描述[::]\s*(.+?)(?:\n|$)', content)
                    if desc_match and "description" not in problem_info:
                        problem_info["description"] = desc_match.group(1).strip()
                    input_match = re.search(r'输入[格式]?[::]\s*(.+?)(?:\n|$)', content)
                    if input_match and "input_format" not in problem_info:
                        problem_info["input_format"] = input_match.group(1).strip()
                    output_match = re.search(r'输出[格式]?[::]\s*(.+?)(?:\n|$)', content)
                    if output_match and "output_format" not in problem_info:
                        problem_info["output_format"] = output_match.group(1).strip()
            return problem_info
        except Exception as e:
            logger.error(f"Error extracting recent problem info: {str(e)}")
            return None

    async def _retrieve_problem_info(self, problem_id: Optional[str] = None, user_id: Optional[str] = None) -> str:
        """检索特定题目的详细信息"""
        if not user_id:
            user_id = getattr(self, 'current_user_id', None)
            if not user_id:
                return "错误:未提供用户ID,无法检索记忆"

        if not problem_id:
            recent_memories = await self.memory_manager.query_memories(
                query="最近讨论的编程题目编号是什么",
                user_id=user_id,
                cancellation_token=None
            )
            logger.info(f"Searching for problem ID in memory for user {user_id}")
            problem_id = self._extract_problem_id_from_memory(recent_memories)
            if not problem_id:
                return "未找到最近讨论的题目编号,请提供具体的题目编号"

        problem_memories = await self.memory_manager.query_memories(f"题目 {problem_id} 的详细信息", user_id)

        if not problem_memories or not hasattr(problem_memories, 'results') or not problem_memories.results:
            return f"抱歉,我没有关于题目 {problem_id} 的详细信息.请提供题目描述,我会尽力帮助解答."

        problem_info_str = f"题目 {problem_id}:\n\n"
        for memory_item in problem_memories.results:
            if hasattr(memory_item, 'content'):
                content = str(memory_item.content)
                if problem_id in content:
                    problem_info_str += f"{content}\n\n"
        return problem_info_str

    async def _retrieve_user_history(self, user_id: Optional[str] = None, query: str = "最近的对话记录") -> str:
        """检索用户的历史对话记录"""
        if not user_id:
            user_id = getattr(self, 'current_user_id', None)
            if not user_id:
                return "错误:未提供用户ID,无法检索记忆"

        user_memories = await self.memory_manager.query_memories(
            query=query,
            user_id=user_id,
            cancellation_token=None
        )
        logger.info(f"Retrieved user history for user {user_id}")

        if not user_memories or not hasattr(user_memories, 'results') or not user_memories.results:
            return f"未找到用户 {user_id} 的历史对话记录"

        history = f"用户 {user_id} 的历史对话记录:\n\n"
        for i, memory_item in enumerate(user_memories.results):
            if hasattr(memory_item, 'content'):
                content = str(memory_item.content)
                history += f"{i+1}. {content}\n\n"
        return history

    def _extract_problem_info_from_memories(self, memories: List[Any], problem_id: str) -> Optional[Dict[str, Any]]:
        """从记忆中提取题目信息"""
        if not memories:
            return None

        problem_info = {"problem_id": problem_id}
        for memory_item in memories:
            if not hasattr(memory_item, 'content'):
                continue
            content = str(memory_item.content)
            if problem_id in content:
                title_match = re.search(r'(?:标题|任务名称|题目)[::]\s*(.+?)(?:\n|$)', content)
                if title_match and "title" not in problem_info:
                    problem_info["title"] = title_match.group(1).strip()
                desc_match = re.search(r'(?:描述|冒险描述)[::]\s*(.+?)(?:\n|$)', content)
                if desc_match and "description" not in problem_info:
                    problem_info["description"] = desc_match.group(1).strip()
                input_match = re.search(r'(?:输入格式|魔法输入)[::]\s*(.+?)(?:\n|$)', content)
                if input_match and "input_format" not in problem_info:
                    problem_info["input_format"] = input_match.group(1).strip()
                output_match = re.search(r'(?:输出格式|期待结果)[::]\s*(.+?)(?:\n|$)', content)
                if output_match and "output_format" not in problem_info:
                    problem_info["output_format"] = output_match.group(1).strip()
                if "examples" not in problem_info and ("示例" in content or "样例" in content):
                    problem_info["examples"] = []
                    input_example = re.search(r'(?:输入|输入宝藏)[::]\s*(.+?)(?:\n|$)', content)
                    output_example = re.search(r'(?:输出|输出宝藏)[::]\s*(.+?)(?:\n|$)', content)
                    if input_example and output_example:
                        problem_info["examples"].append({
                            "input_example": input_example.group(1).strip(),
                            "output_example": output_example.group(1).strip()
                        })
        if len(problem_info) <= 1:
            return None
        return problem_info

    async def _prepare_crawl_request_params(self, problem_id: str) -> Optional[Dict[str, Any]]:
        """
        根据题目ID构建抓取请求所需的参数 (URL, content_type, parse_type).
        不执行实际抓取.返回包含这些参数的字典,如果无法构建则返回None.
        """
        try:
            # 假设洛谷题目ID以'P'开头,可以扩展此逻辑以支持更多OJ来源
            # 例如,通过problem_id的前缀或配置来决定base_url_key
            base_url_key = "luogu"
            
            if not hasattr(self, 'problem_url_bases') or not self.problem_url_bases:
                logger.warning("_prepare_crawl_request_params: problem_url_bases not initialized or empty, using default Luogu URL.")
                self.problem_url_bases = {"luogu": "https://www.luogu.com.cn/problem/"} # Fallback

            base_url = self.problem_url_bases.get(base_url_key)

            if not base_url:
                logger.error(f"No base URL configured for source key '{base_url_key}' for problem_id '{problem_id}'.")
                return None

            target_url = f"{base_url.rstrip('/')}/{problem_id}"
            
            # 对于洛谷题目详情,content_type 和 parse_type 通常是固定的
            content_type_val = ContentType.COMPETITION_TOPIC.value
            parse_type_val = ParseType.ITEM_DETAIL.value
            
            logger.info(f"Prepared crawl request params for {problem_id}: URL='{target_url}', ContentType='{content_type_val}', ParseType='{parse_type_val}'")
            return {
                "target_url": target_url,
                "content_type": content_type_val,
                "parse_type": parse_type_val
            }
        except Exception as e:
            logger.error(f"Error preparing crawl request params for {problem_id}: {str(e)}")
            return None

    async def _generate_response(
        self,
        user_id: str,
        user_message: str, # This is the message TA needs to respond to (original user query or context)
        problem_info_data: Optional[Dict[str, Any]], # This is successfully parsed crawled data, or None, or an error dict
        relevant_memories: Dict[str, List[Any]],
        cancellation_token: Optional[CancellationToken] = None,
        crawl_request_params: Optional[Dict[str, Any]] = None # Params if a new crawl might be needed
    ) -> str:
        """
        生成回复.
        - 如果 crawl_request_params 提供,LLM 可能会生成一个对 CrawlerEngineerAgent 的请求消息.
        - 如果 problem_info_data 提供(来自已完成的抓取/记忆或包含错误),LLM 会用它来生成教学回复/错误处理回复.
        - user_message 是指当前需要处理的用户原始请求或引导LLM生成回复的上下文.
        """
        try:
            # Image-only messages (only marker, no other context) should be fully handled in handle_user_message
            if user_message == "[用户发送了一张图片]" and not problem_info_data and not crawl_request_params:
                 logger.info(f"Image-only marker in _generate_response for user {user_id}, returning standard image prompt.")
                 return "我看到您发送了一张图片,但我无法直接查看图片内容.请您简要描述一下图片中的内容,比如这是一道什么题目,或者您想问什么问题,这样我才能更好地帮助您."

            prompt = self._build_response_prompt(
                user_id,
                user_message,
                problem_info_data, # Pass the parsed problem data (or error dict)
                relevant_memories,
                crawl_request_params=crawl_request_params # Pass potential crawl request params
            )

            current_system_message = self.system_message
            if not current_system_message:
                logger.error("System message is not available for TeachingAssistantAgent.")
                current_system_message = "你是一位乐于助人的AI助手." # Generic fallback
            # The erroneous text block from line 529 to 548 has been removed.

            if not hasattr(self, 'model_client') or self.model_client is None:
                logger.error("model_client not initialized")
                return "抱歉,系统暂时无法回应.请稍后再试或联系管理员."

            messages = [
                SystemMessage(content=current_system_message), # Use the correctly fetched or fallback system message
                UserMessage(content=prompt, source="user")
            ]

            try:
                if hasattr(self.model_client, 'create'):
                    response = await self.model_client.create(messages=messages, cancellation_token=cancellation_token)
                elif hasattr(self.model_client, 'complete'):
                    response = await self.model_client.complete(messages=messages, cancellation_token=cancellation_token)
                elif hasattr(self.model_client, 'chat_completion'):
                    response = await self.model_client.chat_completion(messages=messages, cancellation_token=cancellation_token)
                else:
                    response = await self.model_client(messages=messages, cancellation_token=cancellation_token)

                logger.info(f"Model response type: {type(response)}")
                logger.info(f"Model response attributes: {dir(response) if response else 'None'}")
            except Exception as e:
                logger.error(f"Failed to call model_client: {e}")
                raise ValueError(f"Failed to generate response: {e}")

            if not response or not response.content:
                logger.warning(f"Empty response from LLM for user {user_id}")
                return "抱歉,未能生成有效回复.请重新描述您的问题,我会尽力提供帮助."

            response_text = response.content
            response_bytes = len(response_text.encode('utf-8'))
            logger.info(f"Response length: {response_bytes} bytes")

            if response_bytes > 2048:
                logger.warning(f"Response exceeds 2048 bytes limit: {response_bytes} bytes")
                messages.append(UserMessage(
                    content="你的回复超过了2048字节限制,请提供更简洁的版本,只包含最核心的内容,去除所有不必要的解释和修饰语.",
                    source="user"
                ))
                try:
                    if hasattr(self.model_client, 'create'):
                        simplified_response = await self.model_client.create(messages=messages, cancellation_token=cancellation_token)
                    elif hasattr(self.model_client, 'complete'):
                        simplified_response = await self.model_client.complete(messages=messages, cancellation_token=cancellation_token)
                    elif hasattr(self.model_client, 'chat_completion'):
                        simplified_response = await self.model_client.chat_completion(messages=messages, cancellation_token=cancellation_token)
                    else:
                        simplified_response = await self.model_client(messages=messages, cancellation_token=cancellation_token)
                    response_text = simplified_response.content
                    response_bytes = len(response_text.encode('utf-8'))
                    logger.info(f"Simplified response length: {response_bytes} bytes")
                    if response_bytes > 2048:
                        logger.warning(f"Response still exceeds limit after simplification: {response_bytes} bytes")
                        while len(response_text.encode('utf-8')) > 2000:
                            response_text = response_text[:int(len(response_text) * 0.9)]
                        response_text += "\n\n(回复因长度限制被截断)"
                        logger.info(f"Truncated response length: {len(response_text.encode('utf-8'))} bytes")
                except Exception as e:
                    logger.error(f"Error generating simplified response: {e}")
                    while len(response_text.encode('utf-8')) > 2000:
                        response_text = response_text[:int(len(response_text) * 0.9)]
                    response_text += "\n\n(回复因长度限制被截断)"

            response_text = self._format_response_for_wechat(response_text)
            return response_text
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return "抱歉,处理您的请求时出现了问题.请稍后再试或重新提问."

    def _format_response_for_wechat(self, text: str) -> str:
        """将响应文本格式化为微信友好的纯文本格式"""
        try:
            lines = text.split('\n')
            formatted_lines = []
            in_code_block = False
            code_indent = '    '

            for line in lines:
                if line.strip().startswith('```'):
                    if in_code_block:
                        in_code_block = False
                        formatted_lines.append('')
                    else:
                        in_code_block = True
                        lang = line.strip()[3:].strip()
                        if lang:
                            formatted_lines.append(f"// {lang} 代码:")
                            formatted_lines.append('')
                    continue
                if in_code_block:
                    formatted_lines.append(code_indent + line)
                else:
                    line = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', line)
                    formatted_lines.append(line)

            result = '\n'.join(formatted_lines)
            result = re.sub(r'\n{3,}', '\n\n', result)
            result = result.rstrip() + '\n'
            return result
        except Exception as e:
            logger.error(f"Error formatting response for WeChat: {str(e)}")
            return text

    def _build_response_prompt(self, user_id: str, user_message: str, problem_info: Optional[Dict[str, Any]], relevant_memories: Dict[str, List[Any]]) -> str:
        """构建回复提示"""
        logger.debug(f"Building response prompt for user {user_id}")

        if "[用户发送了一张图片]" in user_message:
            prompt = "学生发送了一张图片,但我无法直接查看图片内容.请引导学生描述图片内容,以便我能更好地帮助他们.\n\n"
            prompt += "请回复:'我看到您发送了一张图片,但我无法直接查看图片内容.请您简要描述一下图片中的内容,比如这是一道什么题目,或者您想问什么问题,这样我才能更好地帮助您.'\n\n"
            return prompt
        else:
            prompt = f"学生的C++问题:{user_message}\n\n"

        if problem_info:
            prompt += "题目信息:\n"
            if isinstance(problem_info, dict):
                if "title" in problem_info:
                    prompt += f"标题:{problem_info.get('title', '')}\n"
                if "problem_id" in problem_info:
                    prompt += f"题号:{problem_info.get('problem_id', '')}\n"
                if "description" in problem_info:
                    prompt += f"描述:{problem_info.get('description', '')}\n"
                if "input_format" in problem_info:
                    prompt += f"输入格式:{problem_info.get('input_format', '')}\n"
                if "output_format" in problem_info:
                    prompt += f"输出格式:{problem_info.get('output_format', '')}\n"

                examples = problem_info.get("examples", [])
                if examples and isinstance(examples, list):
                    prompt += "示例:\n"
                    for i, example in enumerate(examples[:2]):
                        if isinstance(example, dict):
                            prompt += f"示例 {i+1}:\n"
                            prompt += f"输入:{example.get('input_example', '')}\n"
                            prompt += f"输出:{example.get('output_example', '')}\n"
                            if example.get('explanation'):
                                explanation = example.get('explanation', '')
                                if len(explanation) > 100:
                                    explanation = explanation[:100] + "..."
                                prompt += f"说明:{explanation}\n"
            else:
                content = str(problem_info)
                if len(content) > 300:
                    content = content[:300] + "..."
                prompt += content
            prompt += "\n"

        # 处理记忆信息 - 按照记忆类型组织
        if relevant_memories:
            # 添加最近的对话历史(优先级最高)
            if relevant_memories.get("recent_conversation"):
                prompt += "最近的对话历史(按时间顺序,最近的在前):\n"
                for i, mem_obj in enumerate(relevant_memories["recent_conversation"][:7]): # 最多7条
                    if hasattr(mem_obj, 'content') and hasattr(mem_obj, 'metadata') and mem_obj.metadata:
                        msg_content_str = str(mem_obj.content)
                        role = mem_obj.metadata.get('role')
                        if role not in ["user", "assistant"]:
                            if "[user]" in msg_content_str.lower():
                                role = "user"
                            elif "[assistant]" in msg_content_str.lower():
                                role = "assistant"
                        
                        if len(msg_content_str) > 150:
                            msg_content_str = msg_content_str[:150] + "..."
                        
                        if role == "user":
                            prompt += f"{i + 1}. 用户: {msg_content_str.replace('[user] ', '', 1)}\n"
                        elif role == "assistant":
                            prompt += f"{i + 1}. 助手: {msg_content_str.replace('[assistant] ', '', 1)}\n"
                prompt += "\n"

            # 添加与当前问题直接相关的记忆
            if relevant_memories.get("topic_related"):
                prompt += "与当前问题直接相关的内容:\n"
                for i, mem_obj in enumerate(relevant_memories["topic_related"][:3]): # 最多3条
                    if hasattr(mem_obj, 'content'):
                        content = str(mem_obj.content)
                        if len(content) > 150:
                            content = content[:150] + "..."
                        if "[user]" in content:
                            prompt += f"{i+1}. 用户曾问: {content.replace('[user] ', '')}\n"
                        elif "[assistant]" in content:
                            prompt += f"{i+1}. 助手曾答: {content.replace('[assistant] ', '')}\n"
                        else:
                            prompt += f"{i+1}. {content}\n"
                prompt += "\n"

            # 添加与概念相关的记忆
            if relevant_memories.get("concept_related"):
                prompt += "相关概念解释:\n"
                for i, mem_obj in enumerate(relevant_memories["concept_related"][:3]): # 最多3条
                    if hasattr(mem_obj, 'content'):
                        content = str(mem_obj.content)
                        if "```" in content:
                            code_start = content.find("```")
                            if code_start != -1:
                                code_end = content.find("```", code_start + 3)
                                if code_end != -1:
                                    code = content[code_start:code_end + 3]
                                    if len(code) > 200:
                                        code = code[:200] + "...\n```"
                                    prompt += f"{i+1}. {code}\n"
                                    continue
                        if len(content) > 150:
                            content = content[:150] + "..."
                        if "[assistant]" in content:
                            prompt += f"{i+1}. {content.replace('[assistant] ', '')}\n"
                        else:
                            prompt += f"{i+1}. {content}\n"
                prompt += "\n"

            prompt += "以上记忆信息仅作为参考,为保障对话的连贯性需要有近及远进行回溯,但务必使用记忆时要判断和当前问题的相关性,如果用户信息没有明确支持具体的题目号(如XX竞赛题或PXXXX),或语意判断带有明显的追问色彩(如那么这道题该如何如何,我应该怎么修改等等之类).则表明需要从最近一两条信息中的主题继续延续对话\n\n"

        # 判断用户请求是否为宽泛的题目请求
        is_general_problem_request = False
        if problem_info and problem_info.get("problem_id"):
            problem_id_in_message = self._extract_problem_id(user_message)
            if problem_id_in_message:
                remaining_message = user_message.replace(problem_id_in_message, "").strip()
                # 认为请求是通用的,如果剩余消息很短或包含暗示一般性请求的关键字
                if len(remaining_message) < 15 or any(kw in remaining_message.lower() for kw in ["讲讲", "这道题", "题目", "explain", "problem", "告诉我关于", "what is", "tell me about", "介绍一下"]):
                    is_general_problem_request = True
        
        if is_general_problem_request and problem_info and problem_info.get("title"): # 确保我们至少有标题可以讨论
            prompt += f"学生请求了解题目 {problem_info.get('problem_id', '')} ({problem_info.get('title', '未知标题')}).请对该题目进行一次系统性的,完整的讲解,包括但不限于:\n"
            prompt += "1.  题目的主要意图和目标.\n"
            prompt += "2.  输入和输出格式的简明解释.\n"
            prompt += "3.  核心的解题思路,所涉及的主要算法或数据结构.\n"
            prompt += "4.  实现过程中的关键步骤或需要注意的陷阱.\n"
            prompt += "5.  一个简洁的伪代码或C++代码片段示例来说明核心逻辑(如果适用且能在长度限制内清晰展示).\n"
            prompt += "请确保讲解清晰,准确,易懂,并严格符合以下所有回复原则和格式要求.\n\n"
        elif problem_info and not problem_info.get("title"): # 爬虫未能成功获取详细信息或返回信息不全
             prompt += f"我已经尝试获取题目 {problem_info.get('problem_id', '')} 的信息,但未能获取到完整的题目描述.这可能是因为题目信息暂时无法访问或不存在.请学生提供更多关于题目的具体内容或他们遇到的具体问题,我将尽力协助.\n\n"
        else: # 用户问题更具体,或没有有效的 problem_info
            prompt += "请根据以上提供的上下文信息(包括用户问题,可能的题目信息片段,以及历史对话),针对学生的具体问题进行准确和清晰的回答.要求:\n"

        prompt += "通用回答要求:\n"
        prompt += "1. 直接回答问题或按要求进行讲解,确保内容与学生请求相关,不做无关扩展.\n"
        prompt += "2. 使用专业,严谨但易于理解的语言,避免使用俚语或过于随意的emoji.\n"
        prompt += "3. 【重要】回复的总长度严格限制在2048字节(大约600-700中文字)以内.如果信息量较大,请务必进行精简,优先保证核心信息的准确性和完整性.对于系统性讲解,请在长度限制内尽可能覆盖上述提到的核心方面.\n"
        prompt += "4. 如果提供C++代码示例,请确保其简单,正确,易懂,并直接使用缩进和空行进行格式化,【禁止】使用Markdown的代码块标记(```).\n"
        prompt += "5. 分步骤解释时,逻辑应清晰,步骤应简洁.\n"
        prompt += "6. 在解释复杂概念时,可以使用恰当的类比,但始终保持内容的专业性和准确性.\n"
        prompt += "7. 回答时要注意对话的连贯性,恰当参考历史交流内容.\n"
        prompt += "8. 【重要】所有回复都必须是纯文本格式,不得包含任何Markdown特殊标记,例如 `**` (粗体), `_` (斜体), `[]()` (链接), `#` (标题) 等.\n"
        prompt += "9. 使用标准的数字(如 1., 2.)或字母(如 a., b.)进行列表或步骤编号,段落间使用单个空行分隔,以保证最佳的可读性.\n"
        prompt += "10. 生成C++代码时,若涉及标准库组件,推荐使用 `using namespace std;` 来简化代码并减少字符数(除非存在明确的命名冲突风险,此时应使用 `std::` 限定符).代码应注重可读性和教学目的.\n"

        logger.debug(f"Final prompt length for user {user_id}: {len(prompt)} characters")
        logger.info(f"Final prompt for user {user_id}: \n{prompt}") # 记录完整的prompt用于调试
        return prompt


# 单元测试代码
if __name__ == "__main__" and os.environ.get('RUN_EXAMPLE'):
    import unittest
    from unittest.mock import patch, MagicMock, AsyncMock

    class TeachingAssistantAgentTest(unittest.TestCase):
        @patch('autogen_ext.models.openai.OpenAIChatCompletionClient')
        @patch('agents.dev_team.crawler_engineer_agent.CrawlerEngineerAgent')
        @patch('agents.customer_service_team.teaching_memory_manager.TeachingMemoryManager')
        @patch('config.get_config')
        def setUp(self, mock_get_config, mock_memory_manager, mock_crawler, mock_client):
            # Mock get_config
            mock_get_config.return_value = {
                'agents': {
                    'teaching_assistant': {
                        'agent_model_name': 'test-ta-model',
                        'agent_model_api_key': 'test-ta-key',
                    },
                    'memory_system': { # Mock memory system config for TeachingMemoryManager
                        'agent_model_name': 'test-memory-model',
                        'agent_model_api_key': 'test-memory-key',
                        'compression_thresholds': {
                            'short_to_medium_time': 10,
                            'medium_to_long_time': 20,
                            'short_to_medium_count': 5,
                            'medium_to_long_count': 10,
                        },
                        'query_config': {
                            'short_term_k': 3,
                            'medium_term_k': 2,
                            'long_term_k': 1,
                        },
                        'maintenance_interval': 1,
                    }
                },
                'api_keys': {
                    'gemini_api_key': 'gemini-test-key'
                }
            }

            self.mock_memory_manager = mock_memory_manager.return_value
            self.mock_memory_manager.add_message = AsyncMock()
            self.mock_memory_manager.retrieve_relevant_memories = AsyncMock(return_value={
                "recent_conversation": [],
                "topic_related": [],
                "concept_related": []
            })
            self.mock_memory_manager.query_memories = AsyncMock(return_value=MagicMock(results=[]))


            self.mock_crawler = mock_crawler.return_value
            self.mock_crawler._crawl_and_parsing_result = AsyncMock(return_value={
                "title": "测试题目",
                "problem_id": "P1000",
                "description": "这是一个测试题目描述",
                "input_format": "输入格式",
                "output_format": "输出格式",
                "examples": [
                    {
                        "input_example": "示例输入",
                        "output_example": "示例输出",
                        "explanation": "示例解释"
                    }
                ]
            })

            self.mock_client = mock_client.return_value
            self.mock_client.create = AsyncMock(return_value=MagicMock(content="这是一个测试回复"))

            self.agent = TeachingAssistantAgent(
                name="teaching_assistant",
                model_client=self.mock_client,
                crawler_agent=self.mock_crawler,
                memory_manager=self.mock_memory_manager
            )

            logging.basicConfig(level=logging.DEBUG)

        def test_extract_problem_id(self):
            self.assertEqual(self.agent._extract_problem_id("请帮我解答P1000这道题"), "P1000")
            self.assertEqual(self.agent._extract_problem_id("我在做P1234,遇到了问题"), "P1234")
            self.assertIsNone(self.agent._extract_problem_id("这道题很难"))
            self.assertIsNone(self.agent._extract_problem_id("P123太短了"))

        async def test_fetch_problem_info(self):
            result = await self.agent._fetch_problem_info("P1000")
            self.assertIsNotNone(result)
            self.assertEqual(result["title"], "测试题目")
            self.assertEqual(result["problem_id"], "P1000")

            self.mock_crawler._crawl_and_parsing_result.side_effect = Exception("测试异常")
            result = await self.agent._fetch_problem_info("P1000")
            self.assertIsNone(result)

        async def test_handle_user_message(self):
            response = await self.agent.handle_user_message("test_user", "请帮我解答P1000")
            self.assertEqual(response, "这是一个测试回复")

            self.mock_memory_manager.add_message.assert_called()
            self.mock_memory_manager.retrieve_relevant_memories.assert_called_once()
            self.mock_crawler._crawl_and_parsing_result.assert_called_once()
            self.mock_client.create.assert_called_once()

        def test_build_response_prompt(self):
            problem_info = {
                "title": "测试题目",
                "problem_id": "P1000",
                "description": "这是一个测试题目描述",
                "input_format": "输入格式",
                "output_format": "输出格式",
                "examples": [
                    {
                        "input_example": "示例输入",
                        "output_example": "示例输出",
                        "explanation": "示例解释"
                    }
                ]
            }

            relevant_memories = {
                "recent_conversation": [
                    MagicMock(content="[user] Previous question.", metadata={'role': 'user', 'created_at': time.time() - 100}),
                    MagicMock(content="[assistant] Previous answer.", metadata={'role': 'assistant', 'created_at': time.time() - 90})
                ],
                "topic_related": [
                    MagicMock(content="Topic related memory.", metadata={'memory_level': 'long_term'})
                ],
                "concept_related": [
                    MagicMock(content="Concept related memory with ```code```.", metadata={'memory_level': 'medium_term'})
                ]
            }

            prompt = self.agent._build_response_prompt("test_user", "测试问题", problem_info, relevant_memories)

            self.assertIn("测试问题", prompt)
            self.assertIn("测试题目", prompt)
            self.assertIn("P1000", prompt)
            self.assertIn("这是一个测试题目描述", prompt)
            self.assertIn("示例输入", prompt)
            self.assertIn("示例输出", prompt)
            self.assertIn("Previous question.", prompt)
            self.assertIn("Previous answer.", prompt)
            self.assertIn("Topic related memory.", prompt)
            self.assertIn("Concept related memory with code.", prompt) # Code block should be handled

    # 运行单元测试
    if __name__ == "__main__":
        unittest.main(argv=['first-arg-is-ignored'], exit=False)

# 示例代码:使用TeachingAssistantAgent处理多用户场景
if __name__ == "__main__":
    import shutil

    async def run_teaching_assistant_example():
        """运行教学助手多用户示例"""
        print("初始化教学助手代理...")

        try:
            config = get_config()
            print(f"获取配置成功: {config.keys()}")

            model_client = OpenAIChatCompletionClient(
                model=config['agents']['teaching_assistant']['agent_model_name'],
                api_key=config['agents']['teaching_assistant']['agent_model_api_key'],
                model_info={
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "family": "unknown",
                }
            )
            print("模型客户端创建成功")

            crawler_agent = CrawlerEngineerAgent()
            print("爬虫工程师代理初始化成功")

            memory_manager = TeachingMemoryManager()
            print("教学记忆管理器初始化成功")

            teaching_assistant = TeachingAssistantAgent(
                name="teaching_assistant_example",
                model_client=model_client,
                crawler_agent=crawler_agent,
                memory_manager=memory_manager
            )
            print("教学助手代理初始化成功")

            users = {
                "user_p1005": {
                    "id": "example_user_001",
                    "questions": [
                        "你能帮我解答洛谷P1005这道题吗？我不太理解题目要求.",
                        "这道题目的核心算法是什么？",
                        "可以按照题目的描述,一步一步给我讲解下解题思路吗？",
                        "如何处理这道题中的大数问题？",
                        "最后,你能帮我归纳总结今天我们交流的内容吗？"
                    ]
                },
                "user_p1003": {
                    "id": "example_user_002",
                    "questions": [
                        "请帮我分析一下洛谷P1010这道题.",
                        "这道题目需要用什么数据结构？",
                        "如何优化算法复杂度？",
                        "有没有类似的题目可以练习？",
                        "最后,你能帮我归纳总结今天我们交流的内容吗？"
                    ]
                },
                "user_dfs": {
                    "id": "example_user_003",
                    "questions": [
                        "Python中深度优先搜索算法怎么实现？",
                        "DFS和BFS有什么区别？",
                        "如何避免DFS中的栈溢出问题？",
                        "结合上面我们交流的内容,你能帮我解答洛谷P1032这道题吗？",
                        "最后,你能帮我归纳总结今天我们交流的内容吗？"
                    ]
                }
            }

            for question_index in range(5):
                for user_type, user_info in users.items():
                    user_id = user_info["id"]
                    question = user_info["questions"][question_index]

                    print(f"\n用户 {user_id} (类型: {user_type}) - 问题 {question_index+1}: {question}")
                    print("-" * 50)

                    try:
                        response = await teaching_assistant.handle_user_message(
                            user_id=user_id,
                            content=question
                        )
                        print(f"教学助手回复:\n{response}")
                    except Exception as e:
                        print(f"处理问题时出错: {e}")
                        import traceback
                        traceback.print_exc()
                    print("=" * 80)

            print("\n清理记忆数据库...")
            try:
                base_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../..", "data", "memories")
                if os.path.exists(base_path):
                    for user_dir in os.listdir(base_path):
                        user_path = os.path.join(base_path, user_dir)
                        if os.path.isdir(user_path):
                            print(f"删除用户记忆目录: {user_path}")
                            shutil.rmtree(user_path)
                    print("记忆数据库清理完成")
                else:
                    print(f"记忆基础路径不存在: {base_path}")
            except Exception as e:
                print(f"清理记忆数据库时出错: {e}")
                import traceback
                traceback.print_exc()

        except Exception as e:
            print(f"初始化或运行过程中出错: {e}")
            import traceback
            traceback.print_exc()

    print("运行教学助手多用户示例...")
    asyncio.run(run_teaching_assistant_example())
