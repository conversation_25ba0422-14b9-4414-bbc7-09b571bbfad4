"""
定义教学记忆管理器 (TeachingMemoryManager) 类，负责封装所有与教学助手相关的记忆管理逻辑。

该类将处理：
- MultiUserMemory 的初始化和配置加载。
- 用户消息的添加和计数管理。
- 记忆压缩的触发和自定义提示设置。
- 多层次记忆的查询和结果处理。
- 对记忆的读写权限控制（为 TeachingAssistantAgent 提供读写，为 ReviewAgent 提供只读）。
"""

import logging
import os
import time
from datetime import datetime
from typing import Optional, Dict, Any, List

from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core import CancellationToken
from autogen_core.memory import MemoryContent, MemoryMimeType

from config import get_config
from utils.memory.multi_user_memory import MultiUserMemory, MultiUserMemoryConfig, MemoryLevel

logger = logging.getLogger(__name__)

class TeachingMemoryManager:
    """
    教学记忆管理器，封装了教学助手与MultiUserMemory的所有交互逻辑。
    """

    def __init__(self):
        """
        初始化教学记忆管理器。
        负责加载记忆配置并初始化MultiUserMemory实例。
        """
        self.memory = self._init_memory_system()
        self._user_message_counts = {} # 用于在代理实例生命周期内跟踪每个用户的消息计数
        self._compression_prompts = {} # 存储压缩提示

        # 在初始化时设置自定义压缩提示
        self._set_custom_compression_prompts_sync()
        logger.info("TeachingMemoryManager initialized.")

    def _init_memory_system(self) -> MultiUserMemory:
        """
        初始化记忆系统。
        从配置文件加载记忆相关的配置，并创建MultiUserMemory实例。
        """
        base_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../..", "data", "memories")
        os.makedirs(base_path, exist_ok=True)

        try:
            config = get_config()
            llm_client = OpenAIChatCompletionClient(
                model=config['agents']['memory_system']['agent_model_name'],
                api_key=config['agents']['memory_system']['agent_model_api_key'],
                model_info={
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "family": "unknown",
                    "structured_output": True,
                }
            )
        except KeyError as e:
            logger.warning(f"Configuration key not found in memory system LLM client: {e}, using default values")
            llm_client = OpenAIChatCompletionClient(
                model="gemini-2.0-pro",
                api_key=get_config()['api_keys']['gemini_api_key'],
                model_info={
                    "vision": True,
                    "function_calling": True,
                    "json_output": True,
                    "family": "unknown",
                    "structured_output": True,
                }
            )

        try:
            short_to_medium_time = config['agents']['memory_system']['compression_thresholds']['short_to_medium_time']
            medium_to_long_time = config['agents']['memory_system']['compression_thresholds']['medium_to_long_time']
            short_to_medium_count = config['agents']['memory_system']['compression_thresholds']['short_to_medium_count']
            medium_to_long_count = config['agents']['memory_system']['compression_thresholds']['medium_to_long_count']
            short_term_k = config['agents']['memory_system']['query_config']['short_term_k']
            medium_term_k = config['agents']['memory_system']['query_config']['medium_term_k']
            long_term_k = config['agents']['memory_system']['query_config']['long_term_k']
            maintenance_interval = config['agents']['memory_system']['maintenance_interval']
        except KeyError as e:
            logger.warning(f"Configuration key not found: {e}, using default values")
            short_to_medium_time = 7 * 24 * 60 * 60
            medium_to_long_time = 60 * 24 * 60 * 60
            short_to_medium_count = 20
            medium_to_long_count = 50
            short_term_k = 10
            medium_term_k = 5
            long_term_k = 3
            maintenance_interval = 5

        logger.info(f"Memory system configuration loaded from config file:")
        logger.info(f"  - Time thresholds: short_to_medium={short_to_medium_time}s, medium_to_long={medium_to_long_time}s")
        logger.info(f"  - Count thresholds: short_to_medium={short_to_medium_count}, medium_to_long={medium_to_long_count}")
        logger.info(f"  - Query config: short_term_k={short_term_k}, medium_term_k={medium_term_k}, long_term_k={long_term_k}")
        logger.info(f"  - Maintenance interval: {maintenance_interval}")

        memory_config = MultiUserMemoryConfig(
            base_path=base_path,
            shared_memory_enabled=False,
            user_isolation="db_path",
            collection_name="teaching_assistant_memory",
            enable_memory_compression=True,
            llm_client=llm_client,
            short_to_medium_threshold=short_to_medium_time,
            medium_to_long_threshold=medium_to_long_time,
            short_to_medium_count_threshold=short_to_medium_count,
            medium_to_long_count_threshold=medium_to_long_count,
            short_term_k=short_term_k,
            medium_term_k=medium_term_k,
            long_term_k=long_term_k,
            maintenance_interval=maintenance_interval
        )

        memory = MultiUserMemory(memory_config)
        logger.info("Memory system initialized.")
        return memory

    def _set_custom_compression_prompts_sync(self) -> None:
        """
        设置自定义记忆压缩提示（同步版本）。
        这些提示将在记忆压缩时应用到用户的专属记忆实例。
        """
        short_to_medium_prompt = """
        你是一位教育记忆专家，负责分析学生的C++学习记录并提取关键信息。
        请分析以下学生的交流记录，并提取以下关键信息：
        1. 学生遇到的主要困难和常见错误
        2. 学生对哪些C++概念理解不清（如循环、变量、指针、引用、类等）
        3. 学生已经掌握的概念
        4. 学生的学习兴趣点和有效的解释方式
        5. 学生的学习进度和理解水平
        6. 哪些解释方式对该学生特别有效，尤其是对于C++特有的概念（如指针、内存管理）

        请简洁地总结这些信息，重点关注如何使C++概念更容易理解。
        """

        medium_to_long_prompt = """
        你是一位教育发展专家，负责评估学生的C++学习进展。
        请分析以下学生的中期学习记录，并提取以下关键信息：
        1. 学生在C++学习中的长期兴趣变化
        2. 学生编程思维的发展情况，尤其是对于C++特有的思维方式
        3. 学生从简单概念到复杂概念的理解进步
        4. 学生在解决C++问题时的思考方式变化
        5. 学生对编程的信心和自主学习能力的发展
        6. 对该学生特别有效的教学方法

        请简洁地总结这些信息，重点关注如何保持学生对C++编程的兴趣和调整教学方法。
        """

        try:
            self._compression_prompts = {
                "short_to_medium": short_to_medium_prompt,
                "medium_to_long": medium_to_long_prompt
            }
            logger.info("Custom compression prompts prepared.")
        except Exception as e:
            logger.warning(f"Failed to prepare compression prompts: {e}")

    async def add_message(self, user_id: str, content: str, role: str, cancellation_token: Optional[CancellationToken] = None) -> None:
        """
        将消息添加到用户记忆。
        负责管理用户消息计数并触发记忆压缩。
        """
        try:
            # 获取用户专属记忆实例
            user_memory_instance = self.memory._get_user_memory(user_id)

            # 应用压缩提示到用户专属记忆实例
            if hasattr(user_memory_instance, 'update_compression_template'):
                try:
                    user_memory_instance.update_compression_template(
                        "short_to_medium",
                        self._compression_prompts["short_to_medium"]
                    )
                    user_memory_instance.update_compression_template(
                        "medium_to_long",
                        self._compression_prompts["medium_to_long"]
                    )
                    logger.info(f"Custom compression templates updated for user {user_id}")
                except Exception as e:
                    logger.warning(f"Failed to update compression templates for user {user_id}: {e}")
            else:
                logger.warning(f"User memory system for {user_id} does not support updating compression templates")

            # 尝试从记忆中查询最高消息计数
            try:
                count_query = "最近的消息"
                count_results = await self.memory.query(
                    query=count_query,
                    user_id=user_id,
                    cancellation_token=cancellation_token
                )

                highest_count = 0
                if hasattr(count_results, 'results') and count_results.results:
                    for memory_item in count_results.results:
                        if hasattr(memory_item, 'metadata') and memory_item.metadata:
                            msg_count = memory_item.metadata.get('message_count', 0)
                            if isinstance(msg_count, (int, float)) and msg_count > highest_count:
                                highest_count = int(msg_count)

                logger.info(f"Retrieved highest message count for user {user_id} from memory: {highest_count}")
                persistent_count = highest_count
            except Exception as e:
                logger.error(f"Error retrieving message count from memory for user {user_id}: {e}", exc_info=True)
                persistent_count = -1

            # 尝试使用get_message_count方法作为备份
            if persistent_count <= 0 and hasattr(user_memory_instance, 'get_message_count'):
                logger.info(f"Attempting to use get_message_count method for user {user_id}")
                try:
                    backup_count = await user_memory_instance.get_message_count()
                    if backup_count > 0:
                        persistent_count = backup_count
                        logger.info(f"Using backup count from get_message_count: {persistent_count}")
                except Exception as e:
                    logger.error(f"Error calling get_message_count for user {user_id}: {e}", exc_info=True)

            session_count = self._user_message_counts.get(user_id, 0)

            if persistent_count > 0 and persistent_count > session_count:
                logger.info(f"Updating session count for user {user_id} from {session_count} to persistent count {persistent_count}")
                session_count = persistent_count

            session_count += 1
            self._user_message_counts[user_id] = session_count

            current_message_metadata_count = session_count
            logger.info(f"User {user_id} new message count: {current_message_metadata_count} (role: {role})")

            metadata = {
                "user_id": user_id,
                "role": role,
                "timestamp": datetime.now().isoformat(),
                "created_at": time.time(),
                "memory_level": "short_term",
                "message_count": current_message_metadata_count,
                "message_pair_id": f"{user_id}_{current_message_metadata_count}"
            }

            if role == "user" and content.startswith("[user]"):
                formatted_content = content
            elif role == "assistant" and content.startswith("[assistant]"):
                formatted_content = content
            else:
                formatted_content = f"[{role}] {content}"

            memory_content = MemoryContent(
                content=formatted_content,
                mime_type=MemoryMimeType.TEXT,
                metadata=metadata
            )

            await self.memory.add(
                content=memory_content,
                user_id=user_id,
                cancellation_token=cancellation_token
            )
            logger.info(f"Added {role} message to memory for user {user_id}")

            await self.check_and_trigger_compression(user_id, current_message_metadata_count, cancellation_token)

        except Exception as e:
            logger.error(f"Error adding message to memory: {str(e)}")

    async def check_and_trigger_compression(self, user_id: str, message_count: int, cancellation_token: Optional[CancellationToken] = None) -> None:
        """
        检查是否需要触发记忆压缩，并在需要时触发。
        """
        try:
            config = get_config()

            try:
                short_to_medium_count = config['agents']['memory_system']['compression_thresholds']['short_to_medium_count']
                medium_to_long_count = config['agents']['memory_system']['compression_thresholds']['medium_to_long_count']
            except KeyError as e:
                logger.warning(f"Configuration key not found in compression check: {e}, using default values")
                short_to_medium_count = 20
                medium_to_long_count = 50

            if message_count >= medium_to_long_count and hasattr(self.memory, 'compress_memories'):
                logger.info(f"Triggering medium to long term compression for user {user_id} (message count: {message_count})")
                await self.memory.compress_memories(
                    user_id=user_id,
                    from_level=MemoryLevel.MEDIUM_TERM,
                    to_level=MemoryLevel.LONG_TERM,
                    cancellation_token=cancellation_token
                )
            elif message_count >= short_to_medium_count and hasattr(self.memory, 'compress_memories'):
                logger.info(f"Triggering short to medium term compression for user {user_id} (message count: {message_count})")
                await self.memory.compress_memories(
                    user_id=user_id,
                    from_level=MemoryLevel.SHORT_TERM,
                    to_level=MemoryLevel.MEDIUM_TERM,
                    cancellation_token=cancellation_token
                )
        except Exception as e:
            logger.error(f"Error checking and triggering compression: {str(e)}")

    async def query_memories(self, query: str, user_id: str, cancellation_token: Optional[CancellationToken] = None) -> Any:
        """
        查询用户的记忆。
        此方法可供TeachingAssistantAgent和ReviewAgent调用。
        """
        try:
            results = await self.memory.query(
                query=query,
                user_id=user_id,
                cancellation_token=cancellation_token
            )
            return results
        except Exception as e:
            logger.error(f"Error querying memories for user {user_id} with query '{query}': {str(e)}")
            return None

    async def retrieve_relevant_memories(self, user_id: str, current_user_message: str, cancellation_token: Optional[CancellationToken] = None) -> Dict[str, List[Any]]:
        """
        执行多层次记忆查询并处理结果，返回结构化的相关记忆。
        """
        logger.info(f"Retrieving relevant memories for user {user_id} with message: {current_user_message[:50]}...")

        # 构建多层次记忆查询策略
        short_term_query = f"""
        请提供最近的完整对话记录，特别是最近几轮的对话:
        1. 必须按时间顺序排列，最近的对话必须排在最前面
        2. 优先返回最近3-5轮的对话，无论内容是否相关
        3. 确保包含用户最近的问题和助手的回答
        4. 排除所有包含"[用户发送了一张图片]"或"图片内容"的消息

        当前问题: {current_user_message}
        """

        topic_query = f"""
        请提供与当前问题直接相关的记忆:
        1. 优先返回与"{current_user_message}"这个具体问题最相关的内容
        2. 包括用户之前可能问过的类似问题
        3. 包括助手之前对类似问题的回答
        4. 排除所有包含"[用户发送了一张图片]"或"图片内容"的消息

        当前问题: {current_user_message}
        """

        concept_query = f"""
        请提供与当前问题中涉及的概念相关的记忆:
        1. 识别当前问题中的关键概念
        2. 返回与这些概念相关的之前解释或讨论
        3. 按相关性排序，最相关的排在前面
        4. 排除所有包含"[用户发送了一张图片]"或"图片内容"的消息

        当前问题: {current_user_message}
        """

        # 执行多层次记忆查询
        short_term_memories_raw = await self.query_memories(
            query=short_term_query,
            user_id=user_id,
            cancellation_token=cancellation_token
        )

        topic_memories_raw = await self.query_memories(
            query=topic_query,
            user_id=user_id,
            cancellation_token=cancellation_token
        )

        concept_memories_raw = await self.query_memories(
            query=concept_query,
            user_id=user_id,
            cancellation_token=cancellation_token
        )

        # 初始化分类记忆列表
        recent_conversation_memories = []
        topic_related_memories = []
        concept_related_memories = []

        # 辅助函数：提取记忆内容和角色
        def _extract_memory_info(memory_item: Any) -> Optional[Dict[str, Any]]:
            if not hasattr(memory_item, 'content'):
                return None
            content = str(memory_item.content)
            role = None
            if hasattr(memory_item, 'metadata') and memory_item.metadata:
                role = memory_item.metadata.get('role')
            
            # Fallback to content-based role detection if metadata role is missing or unclear
            if role not in ["user", "assistant"]:
                if "[user]" in content.lower():
                    role = "user"
                elif "[assistant]" in content.lower():
                    role = "assistant"
            
            # Filter out image-related messages
            if "[用户发送了一张图片]" in content or "[图片内容]" in content or "图片" in content:
                return None

            return {
                "content": content,
                "role": role,
                "metadata": getattr(memory_item, 'metadata', {})
            }

        # 处理短期记忆（最近的对话）
        if short_term_memories_raw and hasattr(short_term_memories_raw, 'results'):
            for memory_item in short_term_memories_raw.results:
                info = _extract_memory_info(memory_item)
                if info and info['role'] in ["user", "assistant"]:
                    recent_conversation_memories.append(memory_item)
            logger.info(f"Retrieved {len(recent_conversation_memories)} recent conversation memories.")

        # 处理主题相关记忆
        if topic_memories_raw and hasattr(topic_memories_raw, 'results'):
            for memory_item in topic_memories_raw.results:
                info = _extract_memory_info(memory_item)
                if info and memory_item not in recent_conversation_memories: # 避免重复
                    topic_related_memories.append(memory_item)
            logger.info(f"Retrieved {len(topic_related_memories)} topic-related memories.")

        # 处理概念相关记忆
        if concept_memories_raw and hasattr(concept_memories_raw, 'results'):
            for memory_item in concept_memories_raw.results:
                info = _extract_memory_info(memory_item)
                if info and memory_item not in recent_conversation_memories and memory_item not in topic_related_memories: # 避免重复
                    concept_related_memories.append(memory_item)
            logger.info(f"Retrieved {len(concept_related_memories)} concept-related memories.")

        # 确保最近对话记忆按时间倒序排列（最新的在前）
        recent_conversation_memories.sort(
            key=lambda mem_obj: mem_obj.metadata.get('created_at', 0.0) if hasattr(mem_obj, 'metadata') and mem_obj.metadata else 0.0,
            reverse=True
        )

        # 返回结构化的记忆结果
        return {
            "recent_conversation": recent_conversation_memories,
            "topic_related": topic_related_memories,
            "concept_related": concept_related_memories
        }

# 单元测试代码
if __name__ == "__main__":
    import unittest
    from unittest.mock import patch, MagicMock, AsyncMock
    import asyncio

    class TeachingMemoryManagerTest(unittest.TestCase):
        @patch('autogen_ext.models.openai.OpenAIChatCompletionClient')
        @patch('utils.memory.multi_user_memory.MultiUserMemory')
        @patch('config.get_config')
        def setUp(self, mock_get_config, mock_multi_user_memory, mock_openai_client):
            # Mock get_config
            mock_get_config.return_value = {
                'agents': {
                    'memory_system': {
                        'agent_model_name': 'test-model',
                        'agent_model_api_key': 'test-key',
                        'compression_thresholds': {
                            'short_to_medium_time': 10,
                            'medium_to_long_time': 20,
                            'short_to_medium_count': 5,
                            'medium_to_long_count': 10,
                        },
                        'query_config': {
                            'short_term_k': 3,
                            'medium_term_k': 2,
                            'long_term_k': 1,
                        },
                        'maintenance_interval': 1,
                    }
                },
                'api_keys': {
                    'gemini_api_key': 'gemini-test-key'
                }
            }

            # Mock OpenAIChatCompletionClient
            self.mock_llm_client = mock_openai_client.return_value

            # Mock MultiUserMemory
            self.mock_memory_instance = mock_multi_user_memory.return_value
            self.mock_memory_instance.add = AsyncMock()
            self.mock_memory_instance.query = AsyncMock(return_value=MagicMock(results=[]))
            self.mock_memory_instance.compress_memories = AsyncMock()
            self.mock_memory_instance._get_user_memory = MagicMock(return_value=MagicMock(
                update_compression_template=MagicMock(),
                get_message_count=AsyncMock(return_value=0) # Default to 0 for message count
            ))

            self.manager = TeachingMemoryManager()

        def test_init_memory_system(self):
            # Test if memory system is initialized correctly
            self.assertIsNotNone(self.manager.memory)
            self.mock_llm_client.assert_called_once_with(
                model='test-model',
                api_key='test-key',
                model_info={
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "family": "unknown",
                    "structured_output": True,
                }
            )
            self.mock_memory_instance._get_user_memory.assert_called() # Called during add_message

        async def test_add_message(self):
            user_id = "test_user_1"
            content = "Hello, assistant!"
            role = "user"

            await self.manager.add_message(user_id, content, role)

            self.mock_memory_instance.add.assert_called_once()
            # Verify content and metadata
            args, kwargs = self.mock_memory_instance.add.call_args
            memory_content = args[0]
            self.assertIn(content, memory_content.content)
            self.assertEqual(memory_content.metadata['user_id'], user_id)
            self.assertEqual(memory_content.metadata['role'], role)
            self.assertEqual(memory_content.metadata['message_count'], 1) # First message

            # Test message count increment
            await self.manager.add_message(user_id, "Another message", "assistant")
            args, kwargs = self.mock_memory_instance.add.call_args
            memory_content = args[0]
            self.assertEqual(memory_content.metadata['message_count'], 2)

        async def test_check_and_trigger_compression(self):
            user_id = "test_user_2"
            # Simulate reaching short_to_medium_count threshold
            await self.manager.check_and_trigger_compression(user_id, 5)
            self.mock_memory_instance.compress_memories.assert_called_once_with(
                user_id=user_id,
                from_level=MemoryLevel.SHORT_TERM,
                to_level=MemoryLevel.MEDIUM_TERM,
                cancellation_token=None
            )
            self.mock_memory_instance.compress_memories.reset_mock()

            # Simulate reaching medium_to_long_count threshold
            await self.manager.check_and_trigger_compression(user_id, 10)
            self.mock_memory_instance.compress_memories.assert_called_once_with(
                user_id=user_id,
                from_level=MemoryLevel.MEDIUM_TERM,
                to_level=MemoryLevel.LONG_TERM,
                cancellation_token=None
            )

        async def test_query_memories(self):
            user_id = "test_user_3"
            query = "test query"
            mock_results = MagicMock(results=[MagicMock(content="memory content")])
            self.mock_memory_instance.query.return_value = mock_results

            results = await self.manager.query_memories(query, user_id)
            self.assertEqual(results, mock_results)
            self.mock_memory_instance.query.assert_called_once_with(
                query=query,
                user_id=user_id,
                cancellation_token=None
            )

        async def test_retrieve_relevant_memories(self):
            user_id = "test_user_4"
            current_message = "What is DFS?"

            # Mock query results for different memory types
            mock_short_term_results = MagicMock(results=[
                MagicMock(content="[user] What is BFS?", metadata={'role': 'user', 'created_at': time.time() - 100}),
                MagicMock(content="[assistant] BFS is a graph traversal algorithm.", metadata={'role': 'assistant', 'created_at': time.time() - 90}),
                MagicMock(content="[user] What is DFS?", metadata={'role': 'user', 'created_at': time.time() - 10}),
            ])
            mock_topic_results = MagicMock(results=[
                MagicMock(content="DFS explanation from previous session.", metadata={'memory_level': 'long_term'}),
            ])
            mock_concept_results = MagicMock(results=[
                MagicMock(content="Code example for DFS traversal.", metadata={'memory_level': 'medium_term'}),
            ])

            self.mock_memory_instance.query.side_effect = [
                mock_short_term_results,
                mock_topic_results,
                mock_concept_results
            ]

            relevant_memories = await self.manager.retrieve_relevant_memories(user_id, current_message)

            self.assertIn("recent_conversation", relevant_memories)
            self.assertIn("topic_related", relevant_memories)
            self.assertIn("concept_related", relevant_memories)

            self.assertEqual(len(relevant_memories["recent_conversation"]), 3)
            self.assertEqual(len(relevant_memories["topic_related"]), 1)
            self.assertEqual(len(relevant_memories["concept_related"]), 1)

            # Verify sorting of recent_conversation (newest first)
            self.assertIn("What is DFS?", str(relevant_memories["recent_conversation"][0].content))

            # Verify no image messages are included
            mock_short_term_results_with_image = MagicMock(results=[
                MagicMock(content="[user] Here is an image [用户发送了一张图片]", metadata={'role': 'user', 'created_at': time.time() - 100}),
                MagicMock(content="[assistant] I cannot see images.", metadata={'role': 'assistant', 'created_at': time.time() - 90}),
            ])
            self.mock_memory_instance.query.side_effect = [
                mock_short_term_results_with_image,
                MagicMock(results=[]),
                MagicMock(results=[])
            ]
            relevant_memories_no_image = await self.manager.retrieve_relevant_memories(user_id, "Another question")
            self.assertEqual(len(relevant_memories_no_image["recent_conversation"]), 0)


    # 运行单元测试
    if __name__ == "__main__":
        unittest.main(argv=['first-arg-is-ignored'], exit=False)